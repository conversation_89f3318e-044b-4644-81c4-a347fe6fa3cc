{"name": "abstract-bookmark", "version": "1.0.0", "description": "Advanced bookmark management and export tool for social media platforms", "main": "background/service-worker.js", "type": "module", "scripts": {"build": "node scripts/build-extension.js", "build:webpack": "webpack --mode=production", "build:dev": "webpack --mode=development", "build:obfuscated": "node build-obfuscated.cjs", "watch": "webpack --mode=development --watch", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest tests/integration/", "lint": "eslint . --ext .js,.ts", "lint:fix": "eslint . --ext .js,.ts --fix", "format": "prettier --write .", "validate": "npm run lint && npm run test && npm run build", "dev": "concurrently \"npm run watch\" \"web-ext run --source-dir=dist\"", "package": "web-ext build --source-dir=dist --artifacts-dir=packages", "package:obfuscated": "npm run build:obfuscated", "start": "npm run build && echo 'Extension built! Load the dist/ folder in Chrome.'"}, "keywords": ["chrome-extension", "bookmarks", "social-media", "export", "twitter", "linkedin", "instagram", "reddit"], "author": "AbstractBookmark Team", "license": "MIT", "devDependencies": {"@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0", "@types/chrome": "^0.0.246", "@types/jest": "^27.5.2", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "babel-loader": "^9.1.3", "concurrently": "^8.2.1", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.8.1", "eslint": "^8.50.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "fs-extra": "^11.1.1", "glob": "^11.0.3", "html-webpack-plugin": "^5.5.3", "javascript-obfuscator": "^4.1.1", "jest": "^27.5.1", "jest-chrome": "^0.8.0", "jest-environment-jsdom": "^27.5.1", "mini-css-extract-plugin": "^2.7.6", "prettier": "^3.0.3", "style-loader": "^3.3.3", "terser-webpack-plugin": "^5.3.14", "typescript": "^5.2.2", "web-ext": "^7.8.0", "webpack": "^5.100.2", "webpack-cli": "^5.1.4"}, "dependencies": {"crypto-js": "^4.1.1", "date-fns": "^2.30.0", "lodash": "^4.17.21"}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/tests/setup.js"], "collectCoverageFrom": ["background/**/*.js", "content/**/*.js", "popup/**/*.js", "options/**/*.js", "!**/*.test.js", "!**/node_modules/**"], "coverageThreshold": {"global": {"branches": 90, "functions": 90, "lines": 90, "statements": 90}}}, "eslintConfig": {"env": {"browser": true, "es2021": true, "webextensions": true, "jest": true}, "extends": ["eslint:recommended", "@typescript-eslint/recommended", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["@typescript-eslint", "prettier"], "rules": {"prettier/prettier": "error", "no-console": "warn", "no-unused-vars": "error", "prefer-const": "error", "no-var": "error", "object-shorthand": "error", "prefer-arrow-callback": "error"}, "globals": {"chrome": "readonly"}}, "prettier": {"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2, "useTabs": false}, "browserslist": ["last 2 Chrome versions", "last 2 Edge versions", "last 2 Opera versions"]}