import path from 'path';
import CopyWebpackPlugin from 'copy-webpack-plugin';
import HtmlWebpackPlugin from 'html-webpack-plugin';
import MiniCssExtractPlugin from 'mini-css-extract-plugin';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export default (env, argv) => {
  const isProduction = argv.mode === 'production';

  return {
    entry: {
      'background/service-worker': './background/service-worker.js',
      'content/twitter-injector': './content/platforms/twitter-injector.js',
      'content/twitter-api': './content/platforms/twitter-api.js',
      'popup/popup': './popup/popup.js',
      'options/options': './options/options.js',
    },
    output: {
      path: path.resolve(__dirname, 'dist'),
      filename: '[name].js',
      clean: true,
    },
    module: {
      rules: [
        {
          test: /\.js$/,
          exclude: /node_modules/,
          use: {
            loader: 'babel-loader',
            options: {
              presets: ['@babel/preset-env'],
            },
          },
        },
        {
          test: /\.ts$/,
          exclude: /node_modules/,
          use: {
            loader: 'babel-loader',
            options: {
              presets: ['@babel/preset-env', '@babel/preset-typescript'],
            },
          },
        },
        {
          test: /\.css$/,
          use: [
            isProduction ? MiniCssExtractPlugin.loader : 'style-loader',
            'css-loader',
          ],
        },
        {
          test: /\.(png|jpg|jpeg|gif|svg)$/,
          type: 'asset/resource',
          generator: {
            filename: 'assets/images/[name][ext]',
          },
        },
        {
          test: /\.(woff|woff2|eot|ttf|otf)$/,
          type: 'asset/resource',
          generator: {
            filename: 'assets/fonts/[name][ext]',
          },
        },
      ],
    },
    plugins: [
      new CopyWebpackPlugin({
        patterns: [
          {
            from: 'manifest.json',
            to: 'manifest.json',
          },
          {
            from: 'assets/icons',
            to: 'icons',
          },
          {
            from: 'assets/images',
            to: 'assets/images',
            noErrorOnMissing: true,
          },
          {
            from: '_locales',
            to: '_locales',
            noErrorOnMissing: true,
          },
        ],
      }),
      new HtmlWebpackPlugin({
        template: './popup/popup.html',
        filename: 'popup/popup.html',
        chunks: ['popup/popup'],
        minify: isProduction,
      }),
      new HtmlWebpackPlugin({
        template: './options/options.html',
        filename: 'options/options.html',
        chunks: ['options/options'],
        minify: isProduction,
      }),
      new HtmlWebpackPlugin({
        template: './options/welcome.html',
        filename: 'options/welcome.html',
        chunks: ['options/options'],
        minify: isProduction,
      }),
      ...(isProduction
        ? [
            new MiniCssExtractPlugin({
              filename: '[name].css',
            }),
          ]
        : []),
    ],
    resolve: {
      extensions: ['.js', '.ts'],
      alias: {
        '@': path.resolve(__dirname, './'),
        '@background': path.resolve(__dirname, './background'),
        '@content': path.resolve(__dirname, './content'),
        '@popup': path.resolve(__dirname, './popup'),
        '@options': path.resolve(__dirname, './options'),
        '@assets': path.resolve(__dirname, './assets'),
        '@ui': path.resolve(__dirname, './ui'),
        '@tests': path.resolve(__dirname, './tests'),
      },
    },
    devtool: isProduction ? false : 'cheap-module-source-map',
    optimization: {
      minimize: isProduction,
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
          },
          common: {
            name: 'common',
            minChunks: 2,
            chunks: 'all',
            enforce: true,
          },
        },
      },
    },
    performance: {
      hints: isProduction ? 'warning' : false,
      maxEntrypointSize: 512000,
      maxAssetSize: 512000,
    },
  };
};
