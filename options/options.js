/**
 * AbstractBookmark Options Page
 * Settings and configuration interface
 */

class OptionsController {
  constructor() {
    this.storageManager = null;
    this.currentLanguage = 'en';

    this.initializeI18n();
    this.initializeElements();
    this.loadSettings();
    this.bindEvents();
  }

  initializeI18n() {
    // Initialize i18n for all elements with data-i18n attribute
    const elementsToTranslate = document.querySelectorAll('[data-i18n]');
    elementsToTranslate.forEach((element) => {
      const key = element.getAttribute('data-i18n');
      const message = chrome.i18n.getMessage(key);
      if (message) {
        if (element.tagName === 'TITLE') {
          element.textContent = message;
        } else {
          element.textContent = message;
        }
      }
    });
  }

  initializeElements() {
    this.languageSelect = document.getElementById('languageSelect');
    this.autoDetectLanguageCheckbox =
      document.getElementById('autoDetectLanguage');
    this.languageInfo = document.getElementById('languageInfo');
    this.includeMediaCheckbox = document.getElementById('includeMedia');
    this.includeMetricsCheckbox = document.getElementById('includeMetrics');
    this.expandUrlsCheckbox = document.getElementById('expandUrls');
    this.enableRateLimitCheckbox = document.getElementById('enableRateLimit');
    this.enableRetryCheckbox = document.getElementById('enableRetry');
    this.clearCredentialsCheckbox = document.getElementById('clearCredentials');
    this.anonymizeDataCheckbox = document.getElementById('anonymizeData');
    this.saveButton = document.getElementById('saveSettings');
  }

  async loadSettings() {
    try {
      // Get settings from storage
      const { settings } = await chrome.storage.sync.get('settings');

      if (settings) {
        // Load language setting
        this.currentLanguage = settings.language || 'en';
        this.languageSelect.value = this.currentLanguage;
        this.autoDetectLanguageCheckbox.checked =
          settings.languageAutoDetected !== false;

        // Update UI based on auto-detection setting
        this.updateLanguageUI();

        // Load other settings
        this.includeMediaCheckbox.checked = settings.includeMedia !== false;
        this.includeMetricsCheckbox.checked =
          settings.includeMetadata !== false;
        this.expandUrlsCheckbox.checked = settings.expandUrls !== false;
        this.enableRateLimitCheckbox.checked =
          settings.enableRateLimit !== false;
        this.enableRetryCheckbox.checked = settings.enableRetry !== false;
        this.clearCredentialsCheckbox.checked =
          settings.clearCredentials !== false;
        this.anonymizeDataCheckbox.checked = settings.anonymizeData === true;
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  }

  bindEvents() {
    // Language change event
    this.languageSelect.addEventListener('change', () => {
      this.handleLanguageChange();
    });

    // Auto-detect language change event
    this.autoDetectLanguageCheckbox.addEventListener('change', () => {
      this.handleAutoDetectChange();
      this.updateLanguageUI();
    });

    // Save button event
    this.saveButton.addEventListener('click', () => {
      this.saveSettings();
    });

    // Auto-save on checkbox changes
    const checkboxes = [
      this.autoDetectLanguageCheckbox,
      this.includeMediaCheckbox,
      this.includeMetricsCheckbox,
      this.expandUrlsCheckbox,
      this.enableRateLimitCheckbox,
      this.enableRetryCheckbox,
      this.clearCredentialsCheckbox,
      this.anonymizeDataCheckbox,
    ];

    checkboxes.forEach((checkbox) => {
      checkbox.addEventListener('change', () => {
        this.saveSettings();
      });
    });
  }

  updateLanguageUI() {
    const autoDetectEnabled = this.autoDetectLanguageCheckbox.checked;

    // Enable/disable language select based on auto-detection setting
    this.languageSelect.disabled = autoDetectEnabled;

    // Update info text
    if (autoDetectEnabled) {
      this.languageInfo.textContent =
        chrome.i18n.getMessage('options_languageAutoInfo') ||
        'Language will be automatically detected from your browser settings';
    } else {
      this.languageInfo.textContent =
        chrome.i18n.getMessage('options_languageManualInfo') ||
        'Select your preferred language manually';
    }
  }

  async handleLanguageChange() {
    const newLanguage = this.languageSelect.value;

    if (newLanguage !== this.currentLanguage) {
      this.currentLanguage = newLanguage;

      // Save language setting immediately and disable auto-detection
      try {
        const { settings } = await chrome.storage.sync.get('settings');
        const updatedSettings = {
          ...settings,
          language: newLanguage,
          languageAutoDetected: false, // Disable auto-detection when user manually selects
        };
        await chrome.storage.sync.set({ settings: updatedSettings });

        // Update checkbox to reflect disabled auto-detection
        this.autoDetectLanguageCheckbox.checked = false;
        this.updateLanguageUI();

        // Show language change notification
        this.showSaveConfirmation();

        // Reload page to apply new language
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } catch (error) {
        console.error('Failed to save language setting:', error);
      }
    }
  }

  async handleAutoDetectChange() {
    const autoDetectEnabled = this.autoDetectLanguageCheckbox.checked;

    try {
      const { settings } = await chrome.storage.sync.get('settings');
      const updatedSettings = {
        ...settings,
        languageAutoDetected: autoDetectEnabled,
      };
      await chrome.storage.sync.set({ settings: updatedSettings });

      if (autoDetectEnabled) {
        // If auto-detection is enabled, trigger language detection
        chrome.runtime.sendMessage(
          { action: 'detect_language' },
          (response) => {
            if (response && response.success) {
              // Update the language select to show the detected language
              this.currentLanguage = response.language;
              this.languageSelect.value = response.language;
              this.showSaveConfirmation();

              // Reload page to apply new language if it changed
              setTimeout(() => {
                window.location.reload();
              }, 1000);
            }
          }
        );
      }
    } catch (error) {
      console.error('Failed to save auto-detect setting:', error);
    }
  }

  async saveSettings() {
    try {
      // Get current settings to preserve languageAutoDetected flag
      const { settings: currentSettings } =
        await chrome.storage.sync.get('settings');

      // Collect all settings
      const settings = {
        language: this.currentLanguage,
        languageAutoDetected: this.autoDetectLanguageCheckbox.checked,
        includeMedia: this.includeMediaCheckbox.checked,
        includeMetadata: this.includeMetricsCheckbox.checked,
        expandUrls: this.expandUrlsCheckbox.checked,
        enableRateLimit: this.enableRateLimitCheckbox.checked,
        enableRetry: this.enableRetryCheckbox.checked,
        clearCredentials: this.clearCredentialsCheckbox.checked,
        anonymizeData: this.anonymizeDataCheckbox.checked,

        // Keep existing settings that aren't in the UI
        exportFormat: 'json',
        requestDelay: 1000,
        maxRetries: 3,
        timeoutDuration: 30000,
        theme: 'auto',
        showNotifications: true,
        storeCredentials: false,
        encryptData: true,
        batchSize: 20,
        maxConcurrentRequests: 3,
        maxExportSize: 10000,

        platforms: {
          twitter: {
            enabled: true,
            includeReplies: false,
            includeRetweets: true,
          },
          linkedin: { enabled: false },
          instagram: { enabled: false },
          reddit: { enabled: false },
        },
      };

      // Save to storage
      await chrome.storage.sync.set({
        settings: settings,
        updatedAt: new Date().toISOString(),
      });

      this.showSaveConfirmation();
    } catch (error) {
      console.error('Failed to save settings:', error);
      this.showSaveError();
    }
  }

  showSaveConfirmation() {
    const originalText = this.saveButton.textContent;
    const originalClass = this.saveButton.className;

    this.saveButton.textContent = chrome.i18n.getMessage(
      'options_settingsSaved'
    );
    this.saveButton.className = 'save-button saved';

    setTimeout(() => {
      this.saveButton.textContent = originalText;
      this.saveButton.className = originalClass;
    }, 2000);
  }

  showSaveError() {
    const originalText = this.saveButton.textContent;
    const originalStyle = this.saveButton.style.background;

    this.saveButton.textContent = 'Error saving settings';
    this.saveButton.style.background = '#f56565';

    setTimeout(() => {
      this.saveButton.textContent = originalText;
      this.saveButton.style.background = originalStyle;
    }, 2000);
  }
}

// Initialize options when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new OptionsController();
});
