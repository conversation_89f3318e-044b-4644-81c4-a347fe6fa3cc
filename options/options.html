<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title data-i18n="options_title">AbstractBookmark Settings</title>
    <style>
      body {
        font-family:
          -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        max-width: 600px;
        margin: 40px auto;
        padding: 20px;
        line-height: 1.6;
      }
      h1 {
        color: #333;
        border-bottom: 2px solid #1e40af;
        padding-bottom: 10px;
      }
      .setting-group {
        margin: 20px 0;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
      }
      .setting-group h3 {
        margin-top: 0;
        color: #555;
      }
      label {
        display: block;
        margin: 10px 0;
      }
      input[type='checkbox'] {
        margin-right: 8px;
      }
      select {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
      }
      select:disabled {
        background-color: #f5f5f5;
        color: #999;
        cursor: not-allowed;
      }
      .language-info {
        font-size: 12px;
        color: #666;
        margin-top: 5px;
        font-style: italic;
      }
      .save-button {
        background: #1e40af;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 16px;
        margin-top: 20px;
      }
      .save-button:hover {
        background: #1e3a8a;
      }
      .save-button.saved {
        background: #059669;
      }
      .company-branding {
        margin-top: 40px;
        padding: 20px;
        border-top: 1px solid #e2e8f0;
        text-align: center;
        background: #f8fafc;
        border-radius: 8px;
      }
      .company-logo {
        width: 48px;
        height: 48px;
        margin: 0 auto 12px;
        display: block;
        border-radius: 8px;
      }
      .company-info {
        color: #64748b;
        font-size: 14px;
        line-height: 1.5;
      }
      .company-name {
        color: #1e40af;
        font-weight: 600;
        font-size: 16px;
        margin-bottom: 4px;
      }
    </style>
  </head>
  <body>
    <h1 data-i18n="options_title">AbstractBookmark Settings</h1>

    <div class="setting-group">
      <h3 data-i18n="options_languageGroup">Language</h3>
      <label>
        <input type="checkbox" id="autoDetectLanguage" checked />
        <span data-i18n="options_autoDetectLanguage"
          >Automatically detect language from browser settings</span
        >
      </label>
      <label>
        <span data-i18n="options_selectLanguage">Select Language:</span>
        <select id="languageSelect">
          <option value="en">English</option>
          <option value="tr">Türkçe</option>
          <option value="es">Español</option>
          <option value="fr">Français</option>
          <option value="de">Deutsch</option>
          <option value="zh-CN">中文 (简体)</option>
          <option value="ja">日本語</option>
          <option value="ru">Русский</option>
          <option value="hi">हिन्दी</option>
          <option value="pt">Português</option>
          <option value="bn">বাংলা</option>
          <option value="vi">Tiếng Việt</option>
          <option value="th">ไทย</option>
        </select>
        <div
          class="language-info"
          id="languageInfo"
          data-i18n="options_languageInfo"
        >
          Language will be automatically detected from your browser settings
        </div>
      </label>
    </div>

    <div class="setting-group">
      <h3 data-i18n="options_exportOptionsGroup">Export Options</h3>
      <label>
        <input type="checkbox" id="includeMedia" checked />
        <span data-i18n="options_includeMedia"
          >Include media URLs and metadata</span
        >
      </label>
      <label>
        <input type="checkbox" id="includeMetrics" checked />
        <span data-i18n="options_includeMetrics"
          >Include engagement metrics (likes, retweets, etc.)</span
        >
      </label>
      <label>
        <input type="checkbox" id="expandUrls" checked />
        <span data-i18n="options_expandUrls">Expand shortened URLs</span>
      </label>
    </div>

    <div class="setting-group">
      <h3 data-i18n="options_performanceGroup">Performance</h3>
      <label>
        <input type="checkbox" id="enableRateLimit" checked />
        <span data-i18n="options_enableRateLimit"
          >Enable rate limiting protection</span
        >
      </label>
      <label>
        <input type="checkbox" id="enableRetry" checked />
        <span data-i18n="options_enableRetry"
          >Enable automatic retry on errors</span
        >
      </label>
    </div>

    <div class="setting-group">
      <h3 data-i18n="options_privacyGroup">Privacy</h3>
      <label>
        <input type="checkbox" id="clearCredentials" checked />
        <span data-i18n="options_clearCredentials"
          >Clear credentials after export</span
        >
      </label>
      <label>
        <input type="checkbox" id="anonymizeData" />
        <span data-i18n="options_anonymizeData">Anonymize exported data</span>
      </label>
    </div>

    <button
      class="save-button"
      id="saveSettings"
      data-i18n="options_saveSettings"
    >
      Save Settings
    </button>

    <div class="company-branding">
      <img
        src="../assets/icons/aphedra-logo.png"
        alt="Aphedra"
        class="company-logo"
      />
      <div class="company-name">Aphedra</div>
      <div class="company-info">
        AbstractBookmark is developed by Aphedra.<br />
        Advanced bookmark management solutions for modern workflows.
      </div>
      <a href="https://aphedra.tech/abstractbookmark/PRIVACY_POLICY" target="_blank" data-i18n="privacyPolicy">Privacy Policy</a>
    </div>

    <script src="options.js"></script>
  </body>
</html>
