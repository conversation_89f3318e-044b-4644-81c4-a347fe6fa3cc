#!/usr/bin/env node

/**
 * Build Script for AbstractBookmark Extension
 * Prepares the extension for testing and distribution
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');
const distDir = path.join(projectRoot, 'dist');

console.log('🚀 Building AbstractBookmark Extension...\n');

// Clean dist directory
if (fs.existsSync(distDir)) {
  fs.rmSync(distDir, { recursive: true });
  console.log('✅ Cleaned dist directory');
}

fs.mkdirSync(distDir, { recursive: true });

// Copy manifest.json
copyFile('manifest.json', 'manifest.json');
console.log('✅ Copied manifest.json');

// Copy background scripts
copyDirectory('background', 'background');
console.log('✅ Copied background scripts');

// Copy content scripts
copyDirectory('content', 'content');
console.log('✅ Copied content scripts');

// Copy popup files
copyDirectory('popup', 'popup');
console.log('✅ Copied popup files');

// Copy shared utilities
copyDirectory('shared', 'shared');
console.log('✅ Copied shared utilities');

// Copy locales
copyDirectory('_locales', '_locales');
console.log('✅ Copied localization files');

// Copy assets (create placeholder icons if they don't exist)
copyDirectory('assets', 'assets');
createPlaceholderIcons();
console.log('✅ Copied assets and created placeholder icons');

// Create options page if it doesn't exist
createOptionsPage();
console.log('✅ Created options page');

// Validate the build
validateBuild();

console.log('\n🎉 Build completed successfully!');
console.log(`📁 Extension files are in: ${distDir}`);
console.log('\n📋 Next steps:');
console.log('1. Open Chrome and go to chrome://extensions/');
console.log('2. Enable "Developer mode"');
console.log('3. Click "Load unpacked" and select the dist folder');
console.log('4. Navigate to https://x.com/i/bookmarks to test');

function copyFile(src, dest) {
  const srcPath = path.join(projectRoot, src);
  const destPath = path.join(distDir, dest);

  if (fs.existsSync(srcPath)) {
    fs.copyFileSync(srcPath, destPath);
  } else {
    console.warn(`⚠️  Warning: ${src} not found`);
  }
}

function copyDirectory(src, dest) {
  const srcPath = path.join(projectRoot, src);
  const destPath = path.join(distDir, dest);

  if (fs.existsSync(srcPath)) {
    fs.mkdirSync(destPath, { recursive: true });
    copyRecursive(srcPath, destPath);
  } else {
    console.warn(`⚠️  Warning: ${src} directory not found`);
  }
}

function copyRecursive(src, dest) {
  const items = fs.readdirSync(src);

  for (const item of items) {
    const srcItem = path.join(src, item);
    const destItem = path.join(dest, item);

    if (fs.statSync(srcItem).isDirectory()) {
      fs.mkdirSync(destItem, { recursive: true });
      copyRecursive(srcItem, destItem);
    } else {
      fs.copyFileSync(srcItem, destItem);
    }
  }
}

function createPlaceholderIcons() {
  const iconsDir = path.join(distDir, 'icons');
  fs.mkdirSync(iconsDir, { recursive: true });

  const iconSizes = [16, 32, 48, 128];

  // First, try to copy real PNG icons from assets/icons
  for (const size of iconSizes) {
    const sourceIconPath = path.join(
      projectRoot,
      'assets',
      'icons',
      `icon${size}.png`
    );
    const destIconPath = path.join(iconsDir, `icon${size}.png`);

    if (fs.existsSync(sourceIconPath)) {
      fs.copyFileSync(sourceIconPath, destIconPath);
    } else {
      // Create placeholder if real icon doesn't exist
      const svgContent = `
        <svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
          <rect width="${size}" height="${size}" fill="#667eea" rx="4"/>
          <text x="50%" y="50%" text-anchor="middle" dy="0.35em"
                fill="white" font-family="Arial, sans-serif"
                font-size="${Math.floor(size * 0.4)}" font-weight="bold">AB</text>
        </svg>
      `;

      // For now, create a simple text file as placeholder
      // In a real build, you'd use a library like sharp or canvas to create actual PNG files
      fs.writeFileSync(destIconPath.replace('.png', '.svg'), svgContent);

      // Create a note about the missing icon
      const noteContent = `Placeholder icon needed: ${size}x${size} PNG
Replace this with actual icon file: icon${size}.png
SVG template available: icon${size}.svg`;

      fs.writeFileSync(destIconPath.replace('.png', '.txt'), noteContent);
    }
  }
}

function createOptionsPage() {
  const optionsDir = path.join(distDir, 'options');
  fs.mkdirSync(optionsDir, { recursive: true });

  const optionsHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AbstractBookmark Settings</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      max-width: 600px;
      margin: 40px auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1 {
      color: #333;
      border-bottom: 2px solid #667eea;
      padding-bottom: 10px;
    }
    .setting-group {
      margin: 20px 0;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 8px;
    }
    .setting-group h3 {
      margin-top: 0;
      color: #555;
    }
    label {
      display: block;
      margin: 10px 0;
    }
    input[type="checkbox"] {
      margin-right: 8px;
    }
    .save-button {
      background: #667eea;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 5px;
      cursor: pointer;
      font-size: 16px;
    }
    .save-button:hover {
      background: #5a67d8;
    }
  </style>
</head>
<body>
  <h1>AbstractBookmark Settings</h1>
  
  <div class="setting-group">
    <h3>Export Options</h3>
    <label>
      <input type="checkbox" id="includeMedia" checked>
      Include media URLs and metadata
    </label>
    <label>
      <input type="checkbox" id="includeMetrics" checked>
      Include engagement metrics (likes, retweets, etc.)
    </label>
    <label>
      <input type="checkbox" id="expandUrls" checked>
      Expand shortened URLs
    </label>
  </div>
  
  <div class="setting-group">
    <h3>Performance</h3>
    <label>
      <input type="checkbox" id="enableRateLimit" checked>
      Enable rate limiting protection
    </label>
    <label>
      <input type="checkbox" id="enableRetry" checked>
      Enable automatic retry on errors
    </label>
  </div>
  
  <div class="setting-group">
    <h3>Privacy</h3>
    <label>
      <input type="checkbox" id="clearCredentials" checked>
      Clear credentials after export
    </label>
    <label>
      <input type="checkbox" id="anonymizeData">
      Anonymize exported data
    </label>
  </div>
  
  <button class="save-button" id="saveSettings">Save Settings</button>
  
  <script src="options.js"></script>
</body>
</html>`;

  const optionsJs = `// AbstractBookmark Options Page
document.addEventListener('DOMContentLoaded', () => {
  const saveButton = document.getElementById('saveSettings');
  
  // Load saved settings
  chrome.storage.sync.get({
    includeMedia: true,
    includeMetrics: true,
    expandUrls: true,
    enableRateLimit: true,
    enableRetry: true,
    clearCredentials: true,
    anonymizeData: false
  }, (items) => {
    Object.keys(items).forEach(key => {
      const element = document.getElementById(key);
      if (element) {
        element.checked = items[key];
      }
    });
  });
  
  // Save settings
  saveButton.addEventListener('click', () => {
    const settings = {};
    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
    
    checkboxes.forEach(checkbox => {
      settings[checkbox.id] = checkbox.checked;
    });
    
    chrome.storage.sync.set(settings, () => {
      // Show success message
      saveButton.textContent = 'Saved!';
      saveButton.style.background = '#48bb78';
      
      setTimeout(() => {
        saveButton.textContent = 'Save Settings';
        saveButton.style.background = '#667eea';
      }, 2000);
    });
  });
});`;

  fs.writeFileSync(path.join(optionsDir, 'options.html'), optionsHtml);
  fs.writeFileSync(path.join(optionsDir, 'options.js'), optionsJs);
}

function validateBuild() {
  console.log('\n🔍 Validating build...');

  const requiredFiles = [
    'manifest.json',
    'background/service-worker.js',
    'popup/popup.html',
    'popup/popup.css',
    'popup/popup.js',
  ];

  const missingFiles = [];

  for (const file of requiredFiles) {
    const filePath = path.join(distDir, file);
    if (!fs.existsSync(filePath)) {
      missingFiles.push(file);
    }
  }

  if (missingFiles.length > 0) {
    console.error('❌ Missing required files:');
    missingFiles.forEach((file) => console.error(`   - ${file}`));
    process.exit(1);
  }

  // Validate manifest.json
  try {
    const manifestPath = path.join(distDir, 'manifest.json');
    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));

    if (manifest.manifest_version !== 3) {
      console.error('❌ Manifest version must be 3');
      process.exit(1);
    }

    if (!manifest.permissions || !manifest.host_permissions) {
      console.error('❌ Missing required permissions in manifest');
      process.exit(1);
    }

    console.log('✅ Manifest validation passed');
  } catch (error) {
    console.error('❌ Manifest validation failed:', error.message);
    process.exit(1);
  }

  console.log('✅ Build validation passed');
}
