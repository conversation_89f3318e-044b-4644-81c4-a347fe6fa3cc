/**
 * AbstractBookmark - Background Service Worker
 * Complete implementation with export functionality
 */

// Import required modules
import { ExportManager } from './modules/export-manager.js';
import { APIManager } from './modules/api-manager.js';
import { BookmarkManager } from './modules/bookmark-manager.js';
import { StorageManager } from './modules/storage-manager.js';
import { Logger } from './modules/logger.js';

// Service Worker starting

// Initialize managers
const logger = new Logger('ServiceWorker');
const exportManager = new ExportManager();
const apiManager = new APIManager();
const bookmarkManager = new BookmarkManager();
const storageManager = new StorageManager();

// Global credentials storage
let globalCredentials = {};
let bookmarkEndpointUrl = null;

// Initialize storage on startup
storageManager
  .initializeDefaultSettings()
  .then(() => {
    // Initialize language detection after storage is ready
    return storageManager.initializeLanguage();
  })
  .catch((error) => {
    logger.error('Failed to initialize storage:', error);
  });

// Initialize webRequest listeners immediately
initializeWebRequestListeners();

// // Set up periodic language check (every 30 minutes)
// chrome.alarms.create('checkLanguage', { periodInMinutes: 30 });

// // Handle alarm for periodic language check
// chrome.alarms.onAlarm.addListener(async (alarm) => {
//   if (alarm.name === 'checkLanguage') {
//     try {
//       const updated = await storageManager.checkAndUpdateBrowserLanguage();
//       if (updated) {
//         logger.info('Language automatically updated based on browser settings');
//       }
//     } catch (error) {
//       logger.error('Failed to check language on alarm:', error);
//     }
//   }
// });

// Message handler function
async function handleMessage(message, sender, sendResponse) {
  logger.info(`Received message: ${message.action}`);

  try {
    switch (message.action) {
      case 'health_check':
        sendResponse({
          success: true,
          status: 'healthy',
          timestamp: Date.now(),
        });
        break;

      case 'get_supported_platforms':
        const platforms = apiManager.getSupportedPlatforms();
        sendResponse({ success: true, platforms });
        break;

      case 'get_i18n_messages':
        await handleGetI18nMessages(message, sendResponse);
        break;

      case 'detect_language':
        await handleDetectLanguage(message, sendResponse);
        break;

      case 'language_changed':
        await handleLanguageChanged(message, sendResponse);
        break;

      case 'start_export':
        await handleStartExport(message, sendResponse);
        break;

      case 'cancel_export':
        await handleCancelExport(message, sendResponse);
        break;

      case 'get_export_progress':
        await handleGetExportProgress(message, sendResponse);
        break;

      case 'get_statistics':
        await handleGetStatistics(message, sendResponse);
        break;

      case 'check_credentials':
        await handleCheckCredentials(message, sendResponse);
        break;

      case 'force_credential_extraction':
        await handleForceCredentialExtraction(message, sendResponse);
        break;

      case 'force_cleanup_loading_overlay':
        await handleForceCleanupLoadingOverlay(message, sendResponse);
        break;

      default:
        logger.warn('Unknown message action:', message.action);
        sendResponse({
          success: false,
          error: `Unknown action: ${message.action}`,
        });
    }
  } catch (error) {
    logger.error(`Error handling message ${message.action}:`, error);
    sendResponse({ success: false, error: error.message });
  }
}

// Export action handlers
async function handleStartExport(message, sendResponse) {
  const { platform, options } = message;

  if (!apiManager.isPlatformSupported(platform)) {
    sendResponse({
      success: false,
      error: `Unsupported platform: ${platform}`,
    });
    return;
  }

  try {
    const exportId = await exportManager.startExport(platform, options);
    sendResponse({ success: true, exportId });

    // Start the export process in the background
    processExport(exportId, platform, options);
  } catch (error) {
    logger.error('Failed to start export:', error);
    sendResponse({ success: false, error: error.message });
  }
}

async function handleCancelExport(message, sendResponse) {
  const { exportId } = message;

  try {
    await exportManager.cancelExport(exportId);
    sendResponse({ success: true });
  } catch (error) {
    logger.error('Failed to cancel export:', error);
    sendResponse({ success: false, error: error.message });
  }
}

async function handleGetExportProgress(message, sendResponse) {
  const { exportId } = message;

  try {
    const progress = await exportManager.getExportProgress(exportId);
    sendResponse({ success: true, progress });
  } catch (error) {
    logger.error('Failed to get export progress:', error);
    sendResponse({ success: false, error: error.message });
  }
}

async function handleGetStatistics(message, sendResponse) {
  try {
    const statistics = await bookmarkManager.getStatistics();
    sendResponse({ success: true, statistics });
  } catch (error) {
    logger.error('Failed to get statistics:', error);
    sendResponse({ success: false, error: error.message });
  }
}

async function handleCheckCredentials(message, sendResponse) {
  try {
    const hasCredentials =
      globalCredentials.authorization && globalCredentials['x-csrf-token'];
    sendResponse({
      success: true,
      hasCredentials,
      credentials: hasCredentials
        ? {
            hasAuthorization: !!globalCredentials.authorization,
            hasCsrfToken: !!globalCredentials['x-csrf-token'],
            authorizationPreview: globalCredentials.authorization
              ? globalCredentials.authorization.substring(0, 20) + '...'
              : null,
          }
        : null,
    });
  } catch (error) {
    logger.error('Failed to check credentials:', error);
    sendResponse({ success: false, error: error.message });
  }
}

async function handleDetectLanguage(message, sendResponse) {
  try {
    logger.info('Language detection requested');
    const detectedLanguage = await storageManager.detectBrowserLanguage();
    sendResponse({
      success: true,
      language: detectedLanguage,
      message: `Language detected and set to: ${detectedLanguage}`,
    });
  } catch (error) {
    logger.error('Failed to detect language:', error);
    sendResponse({ success: false, error: error.message });
  }
}

async function handleLanguageChanged(message, sendResponse) {
  try {
    const { language } = message;
    logger.info(`Language manually changed to: ${language}`);

    // Optionally trigger any additional actions when language changes
    // For example, update content scripts or notify other parts of the extension

    sendResponse({
      success: true,
      message: `Language changed to: ${language}`,
    });
  } catch (error) {
    logger.error('Failed to handle language change:', error);
    sendResponse({ success: false, error: error.message });
  }
}

async function handleForceCredentialExtraction(message, sendResponse) {
  try {
    logger.info('Force credential extraction requested');

    // Try to extract credentials directly from the active tab
    const tabs = await chrome.tabs.query({
      active: true,
      currentWindow: true,
      url: ['*://twitter.com/*', '*://x.com/*'],
    });

    if (tabs.length > 0) {
      const tab = tabs[0];

      const results = await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        func: () => {
          // Force credential extraction
          function forceExtractCredentials() {
            let authorization = null;
            let csrfToken = null;

            // Extract CSRF from cookies
            const cookies = document.cookie.split(';');
            for (const cookie of cookies) {
              const [name, value] = cookie.trim().split('=');
              if (name === 'ct0') {
                csrfToken = decodeURIComponent(value);
                break;
              }
            }

            // Look for Bearer token in various places
            try {
              // Check localStorage
              for (const key of Object.keys(localStorage)) {
                const value = localStorage.getItem(key);
                if (value && value.includes('Bearer ')) {
                  const match = value.match(/Bearer [A-Za-z0-9%_-]+/);
                  if (match) {
                    authorization = match[0];
                    break;
                  }
                }
              }

              // Check sessionStorage if not found in localStorage
              if (!authorization) {
                for (const key of Object.keys(sessionStorage)) {
                  const value = sessionStorage.getItem(key);
                  if (value && value.includes('Bearer ')) {
                    const match = value.match(/Bearer [A-Za-z0-9%_-]+/);
                    if (match) {
                      authorization = match[0];
                      break;
                    }
                  }
                }
              }
            } catch (e) {
              // Storage access error
            }

            return { authorization, csrfToken, url: window.location.href };
          }

          return forceExtractCredentials();
        },
      });

      if (results && results[0] && results[0].result) {
        const extracted = results[0].result;
        logger.info('Force extraction result:', {
          hasAuth: !!extracted.authorization,
          hasCsrf: !!extracted.csrfToken,
          url: extracted.url,
        });

        if (extracted.authorization && extracted.csrfToken) {
          globalCredentials.authorization = extracted.authorization;
          globalCredentials['x-csrf-token'] = extracted.csrfToken;

          sendResponse({
            success: true,
            extracted: true,
            credentials: {
              hasAuthorization: true,
              hasCsrfToken: true,
              authorizationPreview:
                extracted.authorization.substring(0, 20) + '...',
            },
          });
          return;
        }
      }
    }

    sendResponse({
      success: true,
      extracted: false,
      message: 'Could not extract credentials from current page',
    });
  } catch (error) {
    logger.error('Failed to force credential extraction:', error);
    sendResponse({ success: false, error: error.message });
  }
}

async function handleForceCleanupLoadingOverlay(message, sendResponse) {
  try {
    const { tabId } = message;
    logger.info('Force cleanup loading overlay requested for tab:', tabId);

    // Try to inject a script to remove any stuck loading overlays
    await chrome.scripting.executeScript({
      target: { tabId: tabId },
      func: () => {
        // Remove any loading overlays that might be stuck
        const overlays = document.querySelectorAll(
          '.abstract-bookmark-loading-overlay, .abstract-bookmark-progress-overlay'
        );
        overlays.forEach((overlay) => {
          overlay.remove();
          console.log('Removed stuck loading overlay:', overlay.className);
        });

        // Also clear any timeouts that might be related to loading overlays
        // This is a more aggressive cleanup
        const highestTimeoutId = setTimeout(() => {}, 0);
        for (let i = 0; i < highestTimeoutId; i++) {
          clearTimeout(i);
        }

        return overlays.length;
      },
    });

    sendResponse({
      success: true,
      message: 'Loading overlay cleanup completed',
    });
  } catch (error) {
    logger.error('Failed to cleanup loading overlay:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// Background export processing
async function processExport(exportId, platform, options) {
  try {
    logger.info(`Starting background export process: ${exportId}`);

    // Update status to fetching
    await exportManager.updateExportStatus(exportId, 'fetching', 0);

    // Add credentials to options if available
    const optionsWithCredentials = { ...options };
    if (
      globalCredentials &&
      globalCredentials.authorization &&
      globalCredentials['x-csrf-token']
    ) {
      optionsWithCredentials.credentials = globalCredentials;
      logger.info('Added globalCredentials to API options');
    } else {
      logger.warn('No valid globalCredentials available for API call');
    }

    // Fetch bookmarks from the platform
    const bookmarks = await apiManager.fetchBookmarks(
      platform,
      optionsWithCredentials,
      (progress) => {
        // Update progress during fetching
        exportManager.updateExportStatus(exportId, 'fetching', progress);
      }
    );

    // Update status to processing
    await exportManager.updateExportStatus(exportId, 'processing', 0);

    // Process the bookmarks
    const result = await exportManager.processBookmarks(
      exportId,
      bookmarks,
      options
    );

    // Update status to completed
    await exportManager.updateExportStatus(exportId, 'completed', 100, result);

    // Trigger download
    await triggerDownload(result);

    logger.info(`Export completed successfully: ${exportId}`);
  } catch (error) {
    logger.error(`Export failed: ${exportId}`, error);
    await exportManager.updateExportStatus(
      exportId,
      'failed',
      0,
      null,
      error.message
    );
  }
}

// Trigger file download
async function triggerDownload(exportResult) {
  try {
    const { filename, blob } = exportResult;

    // Convert blob to data URL since URL.createObjectURL is not available in service workers
    const dataUrl = await blobToDataUrl(blob);

    await chrome.downloads.download({
      url: dataUrl,
      filename: filename,
      saveAs: true,
    });

    logger.info(`Download triggered for file: ${filename}`);
  } catch (error) {
    logger.error('Failed to trigger download:', error);
    throw error;
  }
}

// Helper function to convert blob to data URL
function blobToDataUrl(blob) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result);
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
}

// Message listener for content scripts and popup
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  // Handle the message asynchronously
  handleMessage(message, sender, sendResponse);
  return true; // Keep message channel open for async responses
});

// Extension installation handler
chrome.runtime.onInstalled.addListener(async (details) => {
  logger.info('Extension installed/updated:', details.reason);

  if (details.reason === 'install') {
    // First time installation - initialize language detection
    logger.info('First time installation - initializing language detection');
    try {
      await storageManager.initializeLanguage();
    } catch (error) {
      logger.error('Failed to initialize language on install:', error);
    }
  } else if (details.reason === 'update') {
    // Extension updated - check for language changes
    logger.info('Extension updated - checking language settings');
    try {
      await storageManager.checkAndUpdateBrowserLanguage();
    } catch (error) {
      logger.error('Failed to check language on update:', error);
    }
  }
});

// Function to initialize webRequest listeners
function initializeWebRequestListeners() {
  logger.info('Initializing webRequest listeners for credential extraction');

  // WebRequest listeners for credential extraction (like the working old extension)
  chrome.webRequest.onBeforeSendHeaders.addListener(
    function (details) {
      logger.debug('WebRequest intercepted:', details.url);

      // Extract credentials from Twitter API requests
      for (let i = 0; i < details.requestHeaders.length; i++) {
        const header = details.requestHeaders[i];

        if (header.name.toLowerCase() === 'x-csrf-token') {
          globalCredentials['x-csrf-token'] = header.value;
          logger.info('✅ Captured CSRF token from network request');
          logger.info('Current credentials state:', {
            hasAuth: !!globalCredentials.authorization,
            hasCsrf: !!globalCredentials['x-csrf-token'],
          });
        } else if (header.name.toLowerCase() === 'authorization') {
          globalCredentials.authorization = header.value;
          logger.info('✅ Captured authorization token from network request');
          logger.info('Current credentials state:', {
            hasAuth: !!globalCredentials.authorization,
            hasCsrf: !!globalCredentials['x-csrf-token'],
          });
        }
      }

      return { requestHeaders: details.requestHeaders };
    },
    { urls: ['*://x.com/*', '*://twitter.com/*'] },
    ['requestHeaders']
  );

  // Capture bookmark endpoint URLs
  chrome.webRequest.onBeforeRequest.addListener(
    function (details) {
      if (
        details.url.includes('bookmark_timeline') ||
        details.url.includes('Bookmarks')
      ) {
        bookmarkEndpointUrl = details.url;
        logger.info(
          '✅ Captured bookmark endpoint URL:',
          details.url.split('?')[0]
        );
      }
    },
    { urls: ['*://x.com/*', '*://twitter.com/*'] }
  );

  logger.info('WebRequest listeners initialized successfully');
}

// Handle i18n messages request for content scripts
async function handleGetI18nMessages(message, sendResponse) {
  try {
    const language = message.language || 'en';

    // Get messages using Chrome i18n API or load from locale files
    const messages = {};

    // Try to load messages from locale files for the requested language
    try {
      const localeUrl = chrome.runtime.getURL(
        `_locales/${language}/messages.json`
      );
      // Loading locale file
      const response = await fetch(localeUrl);
      if (response.ok) {
        const localeMessages = await response.json();
        // Loaded messages for language
        // Convert locale format to our format
        Object.keys(localeMessages).forEach((key) => {
          messages[key] = { message: localeMessages[key].message };
        });

        sendResponse({
          success: true,
          messages: messages,
          language: language,
        });
        return;
      } else {
        // Failed to fetch locale file
      }
    } catch (localeError) {
      // Could not load locale file, falling back to Chrome i18n
    }

    // Fallback to Chrome i18n API (uses browser UI language)

    // Define message keys for content scripts and popup
    const messageKeys = [
      // App general
      'appName',

      // Content script specific
      'content_exportButton',
      'content_exportProgress',
      'content_exportComplete',
      'content_exportError',
      'content_loadingBookmarks',
      'content_bookmarksFound',
      'content_noBookmarks',
      'content_clickToExport',
      'content_exportInProgress',
      'content_pleaseWait',
      'content_cancel',
      'content_retry',
      'content_close',
      'content_error_networkError',
      'content_error_authError',
      'content_error_rateLimit',
      'content_error_unknown',
      'content_status_initializing',
      'content_status_connecting',
      'content_status_processing',
      'content_status_downloading',
      'content_status_complete',

      // Popup specific
      'popup_detectingPlatform',
      'popup_navigateToBookmarks',
      'popup_openTwitterBookmarks',
      'popup_bookmarksDetected',
      'popup_downloadAllBookmarks',
      'popup_exportAsJson',
      'popup_preparingExport',
      'popup_cancel',
      'popup_exportOptions',
      'popup_exportFormat',
      'popup_formatJson',
      'popup_formatExcel',
      'popup_formatCsv',
      'popup_formatHtml',
      'popup_includeMedia',
      'popup_includeMetrics',
      'popup_expandUrls',
      'popup_exportCompleted',
      'popup_settings',
      'popup_help',

      // Export format specific
      'export_formatJson',
      'export_formatExcel',
      'export_formatCsv',
      'export_formatHtml',
    ];

    // Get messages for each key
    messageKeys.forEach((key) => {
      const message = chrome.i18n.getMessage(key);
      if (message) {
        messages[key] = { message: message };
      }
    });

    sendResponse({
      success: true,
      messages: messages,
      language: language,
    });
  } catch (error) {
    logger.error('Failed to get i18n messages:', error);
    sendResponse({
      success: false,
      error: error.message,
    });
  }
}

logger.info(
  'AbstractBookmark Service Worker initialized with webRequest credential extraction'
);
