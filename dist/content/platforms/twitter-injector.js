/**
 * Twitter Content Script - UI Injector
 * Injects download button into Twitter bookmark pages
 */

// Content script uses Chrome i18n API directly

class TwitterUIInjector {
  constructor() {
    this.logger = {
      info: () => {},
      error: () => {},
      warn: () => {},
      debug: () => {},
    }; // Disabled logging for content scripts
    this.downloadButton = null;
    this.progressOverlay = null;
    this.isInjected = false;
    this.currentExportId = null;
    this.i18n = null;
    this.progressTimeout = null;

    this.init();
  }

  async init() {
    this.logger.info('TwitterUIInjector initializing');

    // Initialize i18n (no need to load external script)
    await this.initializeI18n();

    // Wait for page to load
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () =>
        this.checkAndInject()
      );
    } else {
      this.checkAndInject();
    }

    // Watch for navigation changes (Twitter is a SPA)
    this.observeNavigation();

    // Listen for messages from service worker and popup
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // Keep message channel open for async responses
    });

    // Listen for bookmark count detection requests from popup
    this.setupPopupCommunication();
  }

  async initializeI18n() {
    try {
      // Use Chrome i18n API directly in content script
      this.i18n = {
        getMessage: (key, substitutions = null) => {
          try {
            if (substitutions) {
              return chrome.i18n.getMessage(key, substitutions);
            }
            return chrome.i18n.getMessage(key) || key;
          } catch (error) {
            // i18n: Failed to get message for key
            return key; // Fallback to key
          }
        },
      };

      this.logger.info('Content i18n initialized successfully with Chrome API');
    } catch (error) {
      this.logger.error('Failed to initialize i18n:', error);
      this.i18n = {
        getMessage: (key) => key, // Fallback to key
      };
    }
  }

  setupPopupCommunication() {
    // Handle bookmark count detection requests
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.action === 'detect_bookmark_count') {
        this.detectBookmarkCount()
          .then((count) => {
            sendResponse({
              success: true,
              count: count,
              isBookmarkPage: this.isBookmarkPage(),
            });
          })
          .catch((error) => {
            sendResponse({
              success: false,
              error: error.message,
            });
          });
        return true; // Keep message channel open
      }
    });
  }

  async detectBookmarkCount() {
    try {
      // Try to get count from MAIN world script
      if (
        window.abstractBookmark &&
        window.abstractBookmark.credentialExtractor
      ) {
        const count =
          await window.abstractBookmark.credentialExtractor.detectBookmarkCount();
        if (count !== null) {
          return count;
        }
      }

      // Fallback: count visible bookmark items
      const bookmarkItems = document.querySelectorAll('[data-testid="tweet"]');
      if (bookmarkItems.length > 0) {
        return bookmarkItems.length + '+';
      }

      // Check if there are any timeline indicators
      const timelineElements = document.querySelectorAll(
        '[aria-label*="Timeline"], [data-testid="primaryColumn"]'
      );
      if (timelineElements.length > 0 && this.isBookmarkPage()) {
        return 'Available';
      }

      return null;
    } catch (error) {
      this.logger.warn('Could not detect bookmark count:', error);
      // Don't show user notification for this error as it's not critical
      return null;
    }
  }

  checkAndInject() {
    // Check if we're on a bookmark page
    if (this.isBookmarkPage()) {
      this.injectDownloadButton();
      // Check if auto-start export flag is set
      this.checkAutoStartExport();
    } else {
      this.removeDownloadButton();
    }
  }

  async checkAutoStartExport() {
    try {
      // Check if auto-start export flag is set
      const result = await chrome.storage.local.get(['autoStartExport']);
      if (result.autoStartExport) {
        // Clear the flag
        await chrome.storage.local.remove(['autoStartExport']);

        // Wait a bit for the page to load completely and button to be injected
        setTimeout(() => {
          this.handleDownloadClick();
        }, 2000);
      }
    } catch (error) {
      console.error('Error checking auto-start export flag:', error);
    }
  }

  isBookmarkPage() {
    const url = window.location.href;
    return url.includes('/bookmarks') || url.includes('/i/bookmarks');
  }

  observeNavigation() {
    // Watch for URL changes in SPA
    let lastUrl = window.location.href;

    const startObserver = () => {
      const observer = new MutationObserver(() => {
        const currentUrl = window.location.href;
        if (currentUrl !== lastUrl) {
          lastUrl = currentUrl;
          setTimeout(() => this.checkAndInject(), 1000); // Delay to let page render
        }
      });

      // Make sure document.body exists before observing
      if (document.body) {
        observer.observe(document.body, {
          childList: true,
          subtree: true,
        });
      } else {
        // Wait for body to be available
        setTimeout(startObserver, 100);
      }
    };

    startObserver();

    // Also listen for popstate events
    window.addEventListener('popstate', () => {
      setTimeout(() => this.checkAndInject(), 1000);
    });
  }

  injectDownloadButton() {
    if (this.isInjected) return;

    // Find the right place to inject the button
    const targetContainer = this.findInjectionPoint();
    if (!targetContainer) {
      this.logger.debug('Could not find injection point for download button');
      return;
    }

    this.createDownloadButton();
    targetContainer.appendChild(this.downloadButton);
    this.isInjected = true;

    this.logger.debug('Download button injected successfully');
  }

  findInjectionPoint() {
    // Try multiple selectors to find the right place
    const selectors = [
      '[data-testid="primaryColumn"] > div > div > div > div > div:first-child',
      '[aria-label="Timeline: Bookmarks"] > div:first-child',
      'main[role="main"] section > div > div:first-child',
      '[data-testid="primaryColumn"] h1',
    ];

    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element) {
        // Create a container div if we found a header
        if (element.tagName === 'H1') {
          const container = element.parentElement;
          if (container) return container;
        }
        return element;
      }
    }

    return null;
  }

  createDownloadButton() {
    this.downloadButton = document.createElement('div');
    this.downloadButton.className = 'abstract-bookmark-download-btn';
    this.downloadButton.innerHTML = `
      <div style="
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgb(30, 64, 175);
        color: white;
        border: none;
        border-radius: 9999px;
        padding: 8px 16px;
        margin: 12px 0;
        cursor: pointer;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 15px;
        font-weight: 700;
        transition: background-color 0.2s;
        user-select: none;
        position: relative;
        min-height: 36px;
      " onmouseover="this.style.backgroundColor='rgb(30, 58, 138)'"
         onmouseout="this.style.backgroundColor='rgb(30, 64, 175)'">
        <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor" style="margin-right: 8px;">
          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
        </svg>
        <span id="abstract-bookmark-btn-text" data-content-i18n="content_exportButton">Export Bookmarks</span>
      </div>
    `;

    // Set initial text with i18n
    const buttonText = this.downloadButton.querySelector(
      '#abstract-bookmark-btn-text'
    );
    if (buttonText && this.i18n) {
      buttonText.textContent = this.i18n.getMessage('content_exportButton');
    }

    this.downloadButton.addEventListener('click', () =>
      this.handleDownloadClick()
    );
  }

  async handleDownloadClick() {
    try {
      this.logger.info('Download button clicked');

      // Disable button during export
      this.setButtonState(
        'loading',
        this.i18n.getMessage('content_status_initializing')
      );

      // Send message to service worker to start export
      const response = await chrome.runtime.sendMessage({
        action: 'start_export',
        platform: 'twitter',
        options: {
          maxCount: 10000, // Default max
          includeMedia: true,
          includeMetadata: true,
          exportFormat: 'json',
        },
      });

      if (response.success) {
        this.currentExportId = response.exportId;
        this.setButtonState(
          'exporting',
          this.i18n.getMessage('content_exportProgress')
        );
        this.showProgressOverlay();
        this.resetProgressTimeout(); // Start timeout monitoring
      } else {
        throw new Error(
          response.error || this.i18n.getMessage('error_failedToStartExport')
        );
      }
    } catch (error) {
      this.logger.error('Export failed:', error);
      this.setButtonState('error', this.i18n.getMessage('content_exportError'));
      this.showError(error.message);

      // Reset button after 3 seconds
      setTimeout(() => {
        this.setButtonState(
          'ready',
          this.i18n.getMessage('content_exportButton')
        );
      }, 3000);
    }
  }

  setButtonState(state, text) {
    const buttonText = this.downloadButton?.querySelector(
      '#abstract-bookmark-btn-text'
    );
    if (!buttonText) return;

    buttonText.textContent = text;

    const button = this.downloadButton.querySelector('div');
    if (!button) return;

    switch (state) {
      case 'loading':
      case 'exporting':
        button.style.backgroundColor = 'rgb(100, 116, 139)';
        button.style.cursor = 'not-allowed';
        break;
      case 'error':
        button.style.backgroundColor = 'rgb(244, 33, 46)';
        button.style.cursor = 'not-allowed';
        break;
      case 'success':
        button.style.backgroundColor = 'rgb(5, 150, 105)';
        button.style.cursor = 'pointer';
        break;
      case 'ready':
      default:
        button.style.backgroundColor = 'rgb(30, 64, 175)';
        button.style.cursor = 'pointer';
        break;
    }
  }

  showProgressOverlay() {
    this.progressOverlay = document.createElement('div');
    this.progressOverlay.className = 'abstract-bookmark-progress-overlay';
    this.progressOverlay.innerHTML = `
      <div style="
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      ">
        <div style="
          background: white;
          border-radius: 16px;
          padding: 24px;
          max-width: 400px;
          width: 90%;
          text-align: center;
          box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        ">
          <div style="
            width: 48px;
            height: 48px;
            border: 4px solid rgb(30, 64, 175);
            border-top: 4px solid transparent;
            border-radius: 50%;
            margin: 0 auto 16px;
            animation: spin 1s linear infinite;
          "></div>
          <h3 style="margin: 0 0 8px; font-size: 18px; font-weight: 700;" data-content-i18n="content_exportProgress">Exporting Bookmarks</h3>
          <p id="progress-text" style="margin: 0 0 16px; color: rgb(83, 100, 113);" data-content-i18n="content_status_initializing">Initializing...</p>
          <div style="
            background: rgb(239, 243, 244);
            border-radius: 9999px;
            height: 8px;
            overflow: hidden;
            margin-bottom: 16px;
          ">
            <div id="progress-bar" style="
              background: rgb(30, 64, 175);
              height: 100%;
              width: 0%;
              transition: width 0.3s ease;
            "></div>
          </div>
          <button id="cancel-export" style="
            background: transparent;
            border: 1px solid rgb(207, 217, 222);
            border-radius: 9999px;
            padding: 8px 16px;
            cursor: pointer;
            font-size: 14px;
            color: rgb(15, 20, 25);
          " data-content-i18n="content_cancel">Cancel Export</button>
        </div>
      </div>
      <style>
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      </style>
    `;

    document.body.appendChild(this.progressOverlay);

    // Translate overlay elements
    if (this.i18n) {
      const title = this.progressOverlay.querySelector(
        '[data-content-i18n="content_exportProgress"]'
      );
      const progressText = this.progressOverlay.querySelector(
        '[data-content-i18n="content_status_initializing"]'
      );
      const cancelButton = this.progressOverlay.querySelector(
        '[data-content-i18n="content_cancel"]'
      );

      if (title)
        title.textContent = this.i18n.getMessage('content_exportProgress');
      if (progressText)
        progressText.textContent = this.i18n.getMessage(
          'content_status_initializing'
        );
      if (cancelButton)
        cancelButton.textContent = this.i18n.getMessage('content_cancel');
    }

    // Add cancel functionality
    const cancelButton = this.progressOverlay.querySelector('#cancel-export');
    cancelButton.addEventListener('click', () => this.cancelExport());
  }

  async cancelExport() {
    if (this.currentExportId) {
      try {
        await chrome.runtime.sendMessage({
          action: 'cancel_export',
          exportId: this.currentExportId,
        });
      } catch (error) {
        this.logger.error('Failed to cancel export:', error);
        this.showNotification(
          this.i18n.getMessage('content_error_unknown'),
          'error'
        );
      }
    }

    this.clearProgressTimeout();
    this.hideLoadingOverlay();
    this.setButtonState('ready', 'Export Bookmarks');
    this.currentExportId = null;
  }

  hideProgressOverlay() {
    if (this.progressOverlay) {
      this.progressOverlay.remove();
      this.progressOverlay = null;
    }
  }

  showLoadingOverlay(message = 'Loading...') {
    // Remove existing overlay if any
    this.hideLoadingOverlay();

    this.progressOverlay = document.createElement('div');
    this.progressOverlay.className = 'abstract-bookmark-loading-overlay';
    this.progressOverlay.innerHTML = `
      <div style="
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.6);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        backdrop-filter: blur(4px);
      ">
        <div style="
          background: white;
          border-radius: 20px;
          padding: 32px;
          max-width: 400px;
          width: 90%;
          text-align: center;
          box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
          border: 1px solid rgba(255, 255, 255, 0.2);
        ">
          <div style="
            width: 56px;
            height: 56px;
            border: 4px solid rgb(30, 64, 175);
            border-top: 4px solid transparent;
            border-radius: 50%;
            margin: 0 auto 24px;
            animation: spin 1s linear infinite;
          "></div>

          <h3 style="
            margin: 0 0 12px 0;
            font-size: 18px;
            font-weight: 600;
            color: #1a202c;
          ">Exporting Bookmarks</h3>

          <p id="loading-message" style="
            margin: 0 0 20px 0;
            font-size: 14px;
            color: #64748b;
            line-height: 1.5;
          ">${message}</p>

          <div style="
            width: 100%;
            height: 4px;
            background: #e2e8f0;
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 16px;
          ">
            <div id="loading-progress" style="
              height: 100%;
              background: linear-gradient(90deg, rgb(30, 64, 175), rgb(30, 58, 138));
              border-radius: 2px;
              width: 0%;
              transition: width 0.3s ease;
            "></div>
          </div>

          <p style="
            margin: 0;
            font-size: 12px;
            color: #94a3b8;
          ">Please keep this tab open during export</p>
        </div>
      </div>

      <style>
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      </style>
    `;

    document.body.appendChild(this.progressOverlay);

    // Set up automatic timeout to prevent overlay from staying forever
    this.clearLoadingTimeout();
    this.loadingTimeout = setTimeout(() => {
      console.warn(
        'Loading overlay timeout reached, automatically hiding overlay'
      );
      this.hideLoadingOverlay();
    }, 300000); // 5 minutes timeout
  }

  hideLoadingOverlay() {
    if (this.progressOverlay) {
      this.progressOverlay.remove();
      this.progressOverlay = null;
    }

    // Clear the timeout when manually hiding overlay
    this.clearLoadingTimeout();
  }

  clearLoadingTimeout() {
    if (this.loadingTimeout) {
      clearTimeout(this.loadingTimeout);
      this.loadingTimeout = null;
    }
  }

  updateLoadingMessage(message) {
    if (this.progressOverlay) {
      const messageElement =
        this.progressOverlay.querySelector('#loading-message');
      if (messageElement) {
        messageElement.textContent = message;
      }
    }
  }

  updateLoadingProgress(percentage) {
    if (this.progressOverlay) {
      const progressElement =
        this.progressOverlay.querySelector('#loading-progress');
      if (progressElement) {
        progressElement.style.width = `${Math.min(100, Math.max(0, percentage))}%`;
      }
    }
  }

  updateProgress(progress) {
    if (!this.progressOverlay) return;

    // Update loading message
    if (progress.fetched) {
      this.updateLoadingMessage(
        this.i18n.getMessage('content_bookmarksFound', [progress.fetched])
      );
    } else if (progress.message) {
      this.updateLoadingMessage(progress.message);
    }

    // Update progress bar
    if (progress.percentage) {
      this.updateLoadingProgress(progress.percentage);
    }

    // Update button text if button exists
    const buttonText = this.downloadButton?.querySelector(
      '#abstract-bookmark-btn-text'
    );
    if (buttonText) {
      const percentage = progress.percentage
        ? Math.round(progress.percentage)
        : 0;
      buttonText.textContent = `${this.i18n.getMessage('content_exportProgress')} ${percentage}%`;
    }
  }

  showSuccess(result) {
    this.hideLoadingOverlay();
    this.setButtonState(
      'success',
      this.i18n.getMessage('content_exportComplete')
    );

    // Show success message
    this.showNotification(
      this.i18n.getMessage('content_status_complete'),
      'success'
    );

    // Reset button after 3 seconds
    setTimeout(() => {
      this.setButtonState(
        'ready',
        this.i18n.getMessage('content_exportButton')
      );
    }, 3000);
  }

  showError(message) {
    this.hideLoadingOverlay();
    this.showNotification(
      `${this.i18n.getMessage('content_exportError')}: ${message}`,
      'error'
    );
  }

  showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${type === 'error' ? 'rgb(244, 33, 46)' : type === 'success' ? 'rgb(5, 150, 105)' : 'rgb(30, 64, 175)'};
      color: white;
      padding: 12px 16px;
      border-radius: 8px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      z-index: 10001;
      max-width: 300px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
      notification.remove();
    }, 5000);
  }

  resetProgressTimeout() {
    // Clear existing timeout
    this.clearProgressTimeout();

    // Set new timeout - if no progress for 30 seconds, assume export stopped
    this.progressTimeout = setTimeout(() => {
      console.warn(
        'No progress updates received for 30 seconds, assuming export stopped'
      );
      this.showError('Export stopped responding');
      this.currentExportId = null;
    }, 30000);
  }

  clearProgressTimeout() {
    if (this.progressTimeout) {
      clearTimeout(this.progressTimeout);
      this.progressTimeout = null;
    }
  }

  handleMessage(message, sender, sendResponse) {
    switch (message.action) {
      case 'export_progress':
        if (message.exportId === this.currentExportId) {
          this.updateProgress(message);
          this.resetProgressTimeout();
        }
        break;

      case 'export_complete':
        if (message.exportId === this.currentExportId) {
          this.clearProgressTimeout();
          this.showSuccess(message.result);
          this.currentExportId = null;
        }
        break;

      case 'export_error':
        if (message.exportId === this.currentExportId) {
          this.clearProgressTimeout();
          this.showError(message.error);
          this.currentExportId = null;
        }
        break;

      case 'show_loading_overlay':
        this.showLoadingOverlay(message.message || 'Loading...');
        sendResponse({ success: true });
        break;

      case 'hide_loading_overlay':
        this.hideLoadingOverlay();
        sendResponse({ success: true });
        break;
    }
  }

  removeDownloadButton() {
    if (this.downloadButton) {
      this.downloadButton.remove();
      this.downloadButton = null;
      this.isInjected = false;
    }
  }

  cleanup() {
    this.removeDownloadButton();
    this.hideProgressOverlay();
  }
}

// Initialize the injector
new TwitterUIInjector();
