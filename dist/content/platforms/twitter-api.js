/**
 * Twitter Content Script - API Access (MAIN World)
 * Runs in MAIN world to access Twitter's internal APIs and credentials
 */

class TwitterCredentialExtractor {
  constructor() {
    this.logger = console;
    this.credentials = null;
    this.bookmarkCount = null;
    this.isBookmarkPage = false;
    this.init();
  }

  init() {
    this.logger.info('TwitterCredentialExtractor initializing in MAIN world');

    // Set up global namespace for communication with popup
    if (!window.abstractBookmark) {
      window.abstractBookmark = {};
    }
    window.abstractBookmark.credentialExtractor = this;

    // Listen for credential extraction requests
    window.addEventListener('abstractBookmark:extractCredentials', (event) => {
      this.handleCredentialRequest(event);
    });

    // Listen for bookmark count detection requests
    window.addEventListener('abstractBookmark:detectBookmarkCount', (event) => {
      this.handleBookmarkCountRequest(event);
    });

    // Check if we're on a bookmark page
    this.checkBookmarkPage();

    // Try to extract credentials on load
    this.extractCredentials();
  }

  checkBookmarkPage() {
    const url = window.location.href;
    this.isBookmarkPage =
      url.includes('/bookmarks') || url.includes('/i/bookmarks');

    if (this.isBookmarkPage) {
      this.logger.info('Detected Twitter bookmark page');
      // Try to detect bookmark count after page loads
      setTimeout(() => this.detectBookmarkCount(), 2000);
    }
  }

  async detectBookmarkCount() {
    try {
      // Method 1: Look for bookmark count in page headers
      const selectors = [
        '[data-testid="primaryColumn"] h1',
        '[data-testid="primaryColumn"] h2',
        'h1[role="heading"]',
        'h2[role="heading"]',
        '[aria-label*="bookmark"]',
      ];

      for (const selector of selectors) {
        const elements = document.querySelectorAll(selector);
        for (const element of elements) {
          const text = element.textContent || '';
          if (text.toLowerCase().includes('bookmark')) {
            const match = text.match(/(\d+)/);
            if (match) {
              this.bookmarkCount = parseInt(match[1]);
              this.logger.info(
                'Detected bookmark count from header:',
                this.bookmarkCount
              );
              return this.bookmarkCount;
            }
          }
        }
      }

      // Method 2: Count visible bookmark items
      const bookmarkItems = document.querySelectorAll('[data-testid="tweet"]');
      if (bookmarkItems.length > 0) {
        this.bookmarkCount = bookmarkItems.length;
        this.logger.info(
          'Estimated bookmark count from visible items:',
          this.bookmarkCount
        );
        return this.bookmarkCount;
      }

      // Method 3: Look for timeline indicators
      const timelineElements = document.querySelectorAll(
        '[aria-label*="Timeline"]'
      );
      if (timelineElements.length > 0) {
        this.bookmarkCount = '10+'; // Indicate there are bookmarks but count unknown
        this.logger.info('Detected bookmarks present but count unknown');
        return this.bookmarkCount;
      }
    } catch (error) {
      this.logger.warn('Could not detect bookmark count:', error);
    }

    return null;
  }

  handleBookmarkCountRequest(event) {
    const count = this.detectBookmarkCount();

    // Send response back
    window.dispatchEvent(
      new CustomEvent('abstractBookmark:bookmarkCountResponse', {
        detail: {
          success: count !== null,
          count: count,
          isBookmarkPage: this.isBookmarkPage,
        },
      })
    );
  }

  async extractCredentials() {
    try {
      this.logger.debug(
        'Attempting to extract Twitter credentials as fallback'
      );

      // Method 1: Try to get from existing credentials
      if (this.credentials) {
        return this.credentials;
      }

      // Method 2: Extract from localStorage/sessionStorage
      const storageCredentials = this.extractFromStorage();

      if (storageCredentials) {
        this.credentials = storageCredentials;
        this.logger.debug('Successfully extracted credentials from storage');
        return storageCredentials;
      }

      // Method 3: Extract from page scripts
      const scriptCredentials = this.extractFromPageScripts();

      if (scriptCredentials) {
        this.credentials = scriptCredentials;
        this.logger.debug(
          'Successfully extracted credentials from page scripts'
        );
        return scriptCredentials;
      }

      // Method 4: Try to extract from cookies and current page state
      const cookieCredentials = this.extractFromCookiesAndPage();

      if (cookieCredentials) {
        this.credentials = cookieCredentials;
        this.logger.debug(
          'Successfully extracted credentials from cookies and page'
        );
        return cookieCredentials;
      }

      // Silently fail - don't throw error to avoid console spam
      this.logger.debug(
        'Could not extract Twitter credentials using any method'
      );
      return null;
    } catch (error) {
      this.logger.debug('Failed to extract credentials:', error);
      return null;
    }
  }

  extractFromCookiesAndPage() {
    try {
      // Get CSRF token from cookies
      const csrfToken = this.findCSRFToken();

      if (!csrfToken) {
        return null;
      }

      // Try to find authorization token in various places
      let authorization = null;

      // Check if there are any fetch requests we can intercept
      const originalFetch = window.fetch;

      // Look for authorization in window objects
      if (window._twitter_sess && window._twitter_sess.auth_token) {
        authorization = `Bearer ${window._twitter_sess.auth_token}`;
      }

      // If we have both, return them
      if (authorization && csrfToken) {
        return {
          authorization: authorization,
          csrfToken: csrfToken,
        };
      }

      return null;
    } catch (error) {
      this.logger.debug('Error extracting from cookies and page:', error);
      return null;
    }
  }

  async extractFromNetworkRequests() {
    return new Promise((resolve) => {
      // Intercept fetch requests to capture authorization headers
      const originalFetch = window.fetch;
      let foundCredentials = null;

      window.fetch = function (...args) {
        const [url, options] = args;

        // Check if this is a Twitter API request
        if (
          (url && url.includes('api.twitter.com')) ||
          url.includes('x.com/i/api')
        ) {
          const headers = options?.headers || {};

          if (headers.authorization && headers['x-csrf-token']) {
            foundCredentials = {
              authorization: headers.authorization,
              csrfToken: headers['x-csrf-token'],
            };
          }
        }

        return originalFetch.apply(this, args);
      };

      // Wait for a request to happen, or timeout after 10 seconds
      const timeout = setTimeout(() => {
        window.fetch = originalFetch; // Restore original fetch
        resolve(foundCredentials);
      }, 10000);

      // If we find credentials quickly, resolve immediately
      const checkInterval = setInterval(() => {
        if (foundCredentials) {
          clearTimeout(timeout);
          clearInterval(checkInterval);
          window.fetch = originalFetch; // Restore original fetch
          resolve(foundCredentials);
        }
      }, 100);
    });
  }

  extractFromStorage() {
    try {
      // Check localStorage for auth tokens
      const localStorageKeys = Object.keys(localStorage);

      for (const key of localStorageKeys) {
        try {
          const value = localStorage.getItem(key);
          if (value && value.includes('Bearer ')) {
            // Found potential auth token
            const authMatch = value.match(/Bearer [A-Za-z0-9%_-]+/);
            if (authMatch) {
              // Now we need to find CSRF token
              const csrfToken = this.findCSRFToken();
              if (csrfToken) {
                return {
                  authorization: authMatch[0],
                  csrfToken: csrfToken,
                };
              }
            }
          }
        } catch (e) {
          // Skip invalid JSON or other errors
          continue;
        }
      }

      // Check sessionStorage as well
      const sessionStorageKeys = Object.keys(sessionStorage);

      for (const key of sessionStorageKeys) {
        try {
          const value = sessionStorage.getItem(key);
          if (value && value.includes('Bearer ')) {
            const authMatch = value.match(/Bearer [A-Za-z0-9%_-]+/);
            if (authMatch) {
              const csrfToken = this.findCSRFToken();
              if (csrfToken) {
                return {
                  authorization: authMatch[0],
                  csrfToken: csrfToken,
                };
              }
            }
          }
        } catch (e) {
          continue;
        }
      }

      return null;
    } catch (error) {
      this.logger.debug('Error extracting from storage:', error);
      return null;
    }
  }

  extractFromPageScripts() {
    try {
      // Look for Twitter's configuration objects in the page
      const scripts = document.querySelectorAll('script');

      for (const script of scripts) {
        const content = script.textContent || script.innerHTML;

        // Look for bearer token patterns
        const bearerMatch = content.match(/Bearer [A-Za-z0-9%_-]+/);
        if (bearerMatch) {
          const csrfToken = this.findCSRFToken();
          if (csrfToken) {
            return {
              authorization: bearerMatch[0],
              csrfToken: csrfToken,
            };
          }
        }
      }

      return null;
    } catch (error) {
      this.logger.debug('Error extracting from page scripts:', error);
      return null;
    }
  }

  findCSRFToken() {
    try {
      // Method 1: Look for CSRF token in meta tags
      const csrfMeta = document.querySelector('meta[name="csrf-token"]');
      if (csrfMeta) {
        return csrfMeta.getAttribute('content');
      }

      // Method 2: Look for CSRF token in cookies
      const cookies = document.cookie.split(';');
      for (const cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'ct0' || name === 'csrf_token') {
          return decodeURIComponent(value);
        }
      }

      // Method 3: Look for CSRF token in page scripts
      const scripts = document.querySelectorAll('script');
      for (const script of scripts) {
        const content = script.textContent || script.innerHTML;

        // Look for common CSRF token patterns
        const csrfMatch = content.match(/"csrf_token":"([^"]+)"/);
        if (csrfMatch) {
          return csrfMatch[1];
        }

        const ct0Match = content.match(/"ct0":"([^"]+)"/);
        if (ct0Match) {
          return ct0Match[1];
        }
      }

      // Method 4: Try to extract from window objects
      if (window._twitter_sess) {
        return window._twitter_sess.csrf_token;
      }

      return null;
    } catch (error) {
      this.logger.error('Error finding CSRF token:', error);
      return null;
    }
  }

  handleCredentialRequest(event) {
    const { requestId } = event.detail;

    if (this.credentials) {
      // Send credentials back
      window.dispatchEvent(
        new CustomEvent('abstractBookmark:credentialsResponse', {
          detail: {
            requestId,
            credentials: this.credentials,
            success: true,
          },
        })
      );
    } else {
      // Try to extract credentials now
      this.extractCredentials()
        .then((credentials) => {
          window.dispatchEvent(
            new CustomEvent('abstractBookmark:credentialsResponse', {
              detail: {
                requestId,
                credentials: credentials,
                success: true,
              },
            })
          );
        })
        .catch((error) => {
          window.dispatchEvent(
            new CustomEvent('abstractBookmark:credentialsResponse', {
              detail: {
                requestId,
                error: error.message,
                success: false,
              },
            })
          );
        });
    }
  }

  // Public method to get current credentials
  getCurrentCredentials() {
    return this.credentials;
  }

  // Method to refresh credentials
  async refreshCredentials() {
    this.credentials = null;
    return await this.extractCredentials();
  }
}

/**
 * Twitter Query ID Extractor
 * Extracts GraphQL query IDs from Twitter's JavaScript bundles
 */
class TwitterQueryExtractor {
  constructor() {
    this.logger = console;
    this.queryIds = new Map();
    this.init();
  }

  init() {
    this.logger.info('TwitterQueryExtractor initializing');
    this.extractQueryIds();
  }

  async extractQueryIds() {
    try {
      // Look for Twitter's main JavaScript bundles
      const scripts = document.querySelectorAll('script[src*="main"]');

      for (const script of scripts) {
        const src = script.src;
        if (src && src.includes('main') && src.includes('.js')) {
          try {
            const response = await fetch(src);
            const content = await response.text();

            // Look for bookmark timeline query ID
            const bookmarkMatch = content.match(
              /queryId:"([^"]+)"[^}]*bookmark_timeline/i
            );
            if (bookmarkMatch) {
              this.queryIds.set('bookmark_timeline_v2', bookmarkMatch[1]);
              this.logger.info(
                'Found bookmark timeline query ID:',
                bookmarkMatch[1]
              );
            }

            // Look for other query IDs we might need
            const queryMatches = content.matchAll(/queryId:"([^"]+)"/g);
            for (const match of queryMatches) {
              // Store all query IDs for potential future use
              this.queryIds.set(`query_${this.queryIds.size}`, match[1]);
            }
          } catch (error) {
            this.logger.warn('Failed to fetch script:', src, error);
          }
        }
      }

      // If we didn't find the query ID in scripts, try a fallback method
      if (!this.queryIds.has('bookmark_timeline_v2')) {
        await this.extractFromNetworkRequests();
      }
    } catch (error) {
      this.logger.error('Failed to extract query IDs:', error);
    }
  }

  async extractFromNetworkRequests() {
    return new Promise((resolve) => {
      const originalFetch = window.fetch;
      let found = false;

      window.fetch = function (...args) {
        const [url] = args;

        if (url && url.includes('bookmark_timeline') && !found) {
          // Extract query ID from URL
          const match = url.match(/\/([^\/]+)\/bookmark_timeline/);
          if (match) {
            this.queryIds.set('bookmark_timeline_v2', match[1]);
            this.logger.info(
              'Extracted bookmark query ID from network request:',
              match[1]
            );
            found = true;
          }
        }

        return originalFetch.apply(this, args);
      };

      // Restore original fetch after 30 seconds
      setTimeout(() => {
        window.fetch = originalFetch;
        resolve();
      }, 30000);
    });
  }

  getQueryId(queryName) {
    return this.queryIds.get(queryName);
  }

  getAllQueryIds() {
    return Object.fromEntries(this.queryIds);
  }
}

// Initialize extractors
const credentialExtractor = new TwitterCredentialExtractor();
const queryExtractor = new TwitterQueryExtractor();

// Make them available globally for the extension
window.abstractBookmark = {
  credentialExtractor,
  queryExtractor,

  // Helper methods
  async getCredentials() {
    return await credentialExtractor.extractCredentials();
  },

  getQueryId(queryName) {
    return queryExtractor.getQueryId(queryName);
  },
};
