// AbstractBookmark Options Page
document.addEventListener('DOMContentLoaded', () => {
  const saveButton = document.getElementById('saveSettings');
  
  // Load saved settings
  chrome.storage.sync.get({
    includeMedia: true,
    includeMetrics: true,
    expandUrls: true,
    enableRateLimit: true,
    enableRetry: true,
    clearCredentials: true,
    anonymizeData: false
  }, (items) => {
    Object.keys(items).forEach(key => {
      const element = document.getElementById(key);
      if (element) {
        element.checked = items[key];
      }
    });
  });
  
  // Save settings
  saveButton.addEventListener('click', () => {
    const settings = {};
    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
    
    checkboxes.forEach(checkbox => {
      settings[checkbox.id] = checkbox.checked;
    });
    
    chrome.storage.sync.set(settings, () => {
      // Show success message
      saveButton.textContent = 'Saved!';
      saveButton.style.background = '#48bb78';
      
      setTimeout(() => {
        saveButton.textContent = 'Save Settings';
        saveButton.style.background = '#667eea';
      }, 2000);
    });
  });
});