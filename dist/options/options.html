<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AbstractBookmark Settings</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      max-width: 600px;
      margin: 40px auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1 {
      color: #333;
      border-bottom: 2px solid #667eea;
      padding-bottom: 10px;
    }
    .setting-group {
      margin: 20px 0;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 8px;
    }
    .setting-group h3 {
      margin-top: 0;
      color: #555;
    }
    label {
      display: block;
      margin: 10px 0;
    }
    input[type="checkbox"] {
      margin-right: 8px;
    }
    .save-button {
      background: #667eea;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 5px;
      cursor: pointer;
      font-size: 16px;
    }
    .save-button:hover {
      background: #5a67d8;
    }
  </style>
</head>
<body>
  <h1>AbstractBookmark Settings</h1>
  
  <div class="setting-group">
    <h3>Export Options</h3>
    <label>
      <input type="checkbox" id="includeMedia" checked>
      Include media URLs and metadata
    </label>
    <label>
      <input type="checkbox" id="includeMetrics" checked>
      Include engagement metrics (likes, retweets, etc.)
    </label>
    <label>
      <input type="checkbox" id="expandUrls" checked>
      Expand shortened URLs
    </label>
  </div>
  
  <div class="setting-group">
    <h3>Performance</h3>
    <label>
      <input type="checkbox" id="enableRateLimit" checked>
      Enable rate limiting protection
    </label>
    <label>
      <input type="checkbox" id="enableRetry" checked>
      Enable automatic retry on errors
    </label>
  </div>
  
  <div class="setting-group">
    <h3>Privacy</h3>
    <label>
      <input type="checkbox" id="clearCredentials" checked>
      Clear credentials after export
    </label>
    <label>
      <input type="checkbox" id="anonymizeData">
      Anonymize exported data
    </label>
  </div>
  
  <button class="save-button" id="saveSettings">Save Settings</button>
  
  <script src="options.js"></script>
</body>
</html>