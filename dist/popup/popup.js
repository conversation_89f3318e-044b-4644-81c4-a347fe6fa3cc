/**
 * AbstractBookmark Popup Interface
 * Simplified single-button interface for Twitter bookmark export
 */

class PopupController {
  constructor() {
    this.currentTab = null;
    this.exportInProgress = false;
    this.exportId = null;
    this.i18n = null; // Will hold our custom i18n instance

    this.initialize();
  }

  async initialize() {
    await this.initializeI18n();
    this.initializeElements();
    await this.loadCurrentLanguage();
    this.bindEvents();
    this.checkCurrentTab();
  }

  async initializeI18n() {
    // Initialize our custom i18n system
    this.i18n = new ContentI18n();
    await this.i18n.init();

    // Initialize i18n for all elements with data-i18n attribute
    this.updateAllI18nElements();

    // Listen for language changes
    chrome.storage.onChanged.addListener((changes, namespace) => {
      if (
        namespace === 'sync' &&
        changes.settings &&
        changes.settings.newValue?.language
      ) {
        // Language changed, reinitialize i18n and update UI
        setTimeout(async () => {
          await this.i18n.init();
          this.updateAllI18nElements();
          this.loadCurrentLanguage(); // Update language selector
        }, 100);
      }
    });
  }

  updateAllI18nElements() {
    const elementsToTranslate = document.querySelectorAll('[data-i18n]');
    elementsToTranslate.forEach((element) => {
      const key = element.getAttribute('data-i18n');
      let message;

      // Use our custom i18n system if available, fallback to Chrome's i18n
      if (this.i18n && this.i18n.initialized) {
        message = this.i18n.getMessage(key);
      } else {
        message = chrome.i18n.getMessage(key);
      }

      if (message) {
        element.textContent = message;
      }
    });
  }

  async loadCurrentLanguage() {
    try {
      const { settings } = await chrome.storage.sync.get('settings');
      const currentLanguage = settings?.language || 'en';
      if (this.quickLanguageSelect) {
        this.quickLanguageSelect.value = currentLanguage;
      }
    } catch (error) {
      // Failed to load current language
    }
  }

  initializeElements() {
    // Main elements
    this.platformIndicator = document.getElementById('platformIndicator');
    this.platformStatus = document.getElementById('platformStatus');
    this.platformMessage = document.getElementById('platformMessage');
    this.exportSection = document.getElementById('exportSection');

    // Export elements
    this.bookmarkCount = document.getElementById('bookmarkCount');
    this.exportButton = document.getElementById('exportButton');
    this.buttonLoader = document.getElementById('buttonLoader');
    this.progressSection = document.getElementById('progressSection');
    this.progressFill = document.getElementById('progressFill');
    this.progressText = document.getElementById('progressText');
    this.progressPercentage = document.getElementById('progressPercentage');
    this.cancelButton = document.getElementById('cancelButton');

    // Options elements
    this.optionsToggle = document.getElementById('optionsToggle');
    this.optionsContent = document.getElementById('optionsContent');
    this.exportFormat = document.getElementById('exportFormat');
    this.includeMedia = document.getElementById('includeMedia');
    this.includeMetrics = document.getElementById('includeMetrics');
    this.expandUrls = document.getElementById('expandUrls');

    // Status and footer elements
    this.statusMessage = document.getElementById('statusMessage');
    this.statusIcon = document.getElementById('statusIcon');
    this.statusText = document.getElementById('statusText');
    this.settingsButton = document.getElementById('settingsButton');
    this.helpButton = document.getElementById('helpButton');
    this.quickLanguageSelect = document.getElementById('quickLanguageSelect');
    // quickLanguageSelect element found

    // Bookmark link element
    this.bookmarkLink = document.querySelector('.bookmark-link');

    // Initialize export options as expanded by default
    this.initializeExpandedOptions();
  }

  initializeExpandedOptions() {
    // Ensure the options are expanded by default
    this.optionsContent.style.display = 'block';
    this.optionsToggle.classList.add('expanded');
  }

  bindEvents() {
    // Primary export button
    this.exportButton.addEventListener('click', () => this.startExport());

    // Cancel button
    this.cancelButton.addEventListener('click', () => this.cancelExport());

    // Options toggle
    this.optionsToggle.addEventListener('click', () => this.toggleOptions());

    // Format selection change
    this.exportFormat.addEventListener('change', () =>
      this.updateExportButtonText()
    );

    // Footer buttons
    this.settingsButton.addEventListener('click', () => this.openSettings());
    this.helpButton.addEventListener('click', () => this.openHelp());

    // Quick language selector
    // Adding event listener to quickLanguageSelect
    this.quickLanguageSelect.addEventListener('change', () => {
      // Language selector change event triggered
      this.handleQuickLanguageChange();
    });

    // Bookmark link click handler
    if (this.bookmarkLink) {
      this.bookmarkLink.addEventListener('click', (e) => {
        e.preventDefault();
        this.handleBookmarkLinkClick();
      });
    }

    // Listen for messages from service worker
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
    });
  }

  async handleBookmarkLinkClick() {
    try {
      // Set flag to auto-start export after navigation
      await chrome.storage.local.set({ autoStartExport: true });

      // Navigate to bookmarks page in current tab
      await chrome.tabs.update(this.currentTab.id, {
        url: 'https://x.com/i/bookmarks',
      });

      // Close popup
      window.close();
    } catch (error) {
      console.error('Error navigating to bookmarks:', error);
    }
  }

  async checkCurrentTab() {
    try {
      // Get current active tab
      const [tab] = await chrome.tabs.query({
        active: true,
        currentWindow: true,
      });
      this.currentTab = tab;

      if (!tab || !tab.url) {
        this.showPlatformMessage(
          chrome.i18n.getMessage('error_unableToDetectPage')
        );
        return;
      }

      // Check if on supported platform
      const isTwitter =
        tab.url.includes('twitter.com') || tab.url.includes('x.com');
      const isBookmarkPage =
        tab.url.includes('/bookmarks') || tab.url.includes('/i/bookmarks');

      if (isTwitter && isBookmarkPage) {
        this.showExportInterface();
        this.detectBookmarkCount();
        this.checkCredentials(); // Check if credentials are available
      } else if (isTwitter) {
        this.showPlatformMessage(
          chrome.i18n.getMessage('popup_navigateToBookmarks')
        );
        this.platformStatus.textContent = chrome.i18n.getMessage(
          'status_onTwitterGoToBookmarks'
        );
        this.platformStatus.className = 'platform-status';
        this.checkCredentials(); // Check credentials even on Twitter pages
      } else {
        this.showPlatformMessage(
          chrome.i18n.getMessage('error_twitterNotDetected')
        );
        this.platformStatus.textContent = chrome.i18n.getMessage(
          'status_unsupportedPlatform'
        );
        this.platformStatus.className = 'platform-status unsupported';
      }
    } catch (error) {
      // Error checking current tab
      this.showPlatformMessage(
        chrome.i18n.getMessage('error_detectingPlatform')
      );
    }
  }

  showPlatformMessage(message = null) {
    this.platformMessage.style.display = 'block';
    this.exportSection.style.display = 'none';

    if (message) {
      const messageText = this.platformMessage.querySelector('.message-text');
      messageText.textContent = message;
    }
  }

  showExportInterface() {
    this.platformMessage.style.display = 'none';
    this.exportSection.style.display = 'block';
    this.platformStatus.textContent = chrome.i18n.getMessage(
      'status_twitterBookmarksDetected'
    );
    this.platformStatus.className = 'platform-status supported';
  }

  async detectBookmarkCount() {
    try {
      // Send message to content script to detect bookmark count
      const response = await chrome.tabs.sendMessage(this.currentTab.id, {
        action: 'detect_bookmark_count',
      });

      if (response && response.success) {
        const count = response.count || '--';
        this.bookmarkCount.querySelector('.count-number').textContent = count;
      }
    } catch (error) {
      // Could not detect bookmark count - this is expected if content script isn't loaded yet
    }
  }

  async checkCredentials() {
    try {
      // Check if credentials are available from webRequest
      const response = await chrome.runtime.sendMessage({
        action: 'check_credentials',
      });

      if (response && response.success) {
        if (response.hasCredentials) {
          // Credentials available
          // Update UI to show credentials are ready
          if (this.platformStatus) {
            this.platformStatus.textContent +=
              ' ' + chrome.i18n.getMessage('status_credentialsReady');
            this.platformStatus.style.color = '#1d9bf0';
          }
        } else {
          // No credentials available yet
          // Update UI to show credentials are not ready
          if (this.platformStatus) {
            this.platformStatus.textContent +=
              ' ' + chrome.i18n.getMessage('status_loading');
            this.platformStatus.style.color = '#657786';
          }

          // Try to trigger credential extraction by refreshing the page
          this.showCredentialHelp();
          this.addForceExtractionButton();
        }
      }
    } catch (error) {
      // Could not check credentials
    }
  }

  showCredentialHelp() {
    // Add a help message for credential extraction
    const helpDiv = document.createElement('div');
    helpDiv.style.cssText = `
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      border-radius: 4px;
      padding: 8px;
      margin: 8px 0;
      font-size: 12px;
      color: #856404;
    `;
    helpDiv.innerHTML = `
      <strong>Credentials not ready:</strong><br>
      1. Refresh this Twitter page<br>
      2. Wait for page to load completely<br>
      3. Try export again
    `;

    // Insert after platform status
    if (this.platformStatus && this.platformStatus.parentNode) {
      this.platformStatus.parentNode.insertBefore(
        helpDiv,
        this.platformStatus.nextSibling
      );

      // Remove after 10 seconds
      setTimeout(() => {
        if (helpDiv.parentNode) {
          helpDiv.parentNode.removeChild(helpDiv);
        }
      }, 10000);
    }
  }

  addForceExtractionButton() {
    // Check if button already exists
    if (document.getElementById('force-extraction-btn')) {
      return;
    }

    const button = document.createElement('button');
    button.id = 'force-extraction-btn';
    button.textContent = chrome.i18n.getMessage('button_tryExtractCredentials');
    button.style.cssText = `
      background: #1d9bf0;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 6px 12px;
      font-size: 12px;
      cursor: pointer;
      margin: 8px 0;
      width: 100%;
    `;

    button.addEventListener('click', async () => {
      button.disabled = true;
      button.textContent = chrome.i18n.getMessage('button_extracting');

      try {
        const response = await chrome.runtime.sendMessage({
          action: 'force_credential_extraction',
        });

        if (response && response.success && response.extracted) {
          button.textContent = chrome.i18n.getMessage(
            'button_credentialsExtracted'
          );
          button.style.background = '#00ba7c';

          // Refresh credential status
          setTimeout(() => {
            this.checkCredentials();
            if (button.parentNode) {
              button.parentNode.removeChild(button);
            }
          }, 2000);
        } else {
          button.textContent = chrome.i18n.getMessage(
            'button_extractionFailed'
          );
          button.style.background = '#f91880';
          setTimeout(() => {
            button.disabled = false;
            button.textContent = chrome.i18n.getMessage(
              'button_tryExtractCredentials'
            );
            button.style.background = '#1d9bf0';
          }, 3000);
        }
      } catch (error) {
        // Force extraction error
        button.textContent = chrome.i18n.getMessage('button_error');
        button.style.background = '#f91880';
        setTimeout(() => {
          button.disabled = false;
          button.textContent = chrome.i18n.getMessage(
            'button_tryExtractCredentials'
          );
          button.style.background = '#1d9bf0';
        }, 3000);
      }
    });

    // Insert after platform status
    if (this.platformStatus && this.platformStatus.parentNode) {
      this.platformStatus.parentNode.insertBefore(
        button,
        this.platformStatus.nextSibling
      );
    }
  }

  async startExport() {
    if (this.exportInProgress) return;

    try {
      // Immediately show loading state
      this.exportInProgress = true;
      this.exportButton.classList.add('loading');
      this.exportButton.disabled = true;
      this.updateLoadingMessage(
        chrome.i18n.getMessage('popup_preparingExport')
      );

      // First check if credentials are available
      this.updateLoadingMessage(
        chrome.i18n.getMessage('progress_checkingCredentials')
      );
      const credCheck = await chrome.runtime.sendMessage({
        action: 'check_credentials',
      });

      if (!credCheck.success || !credCheck.hasCredentials) {
        throw new Error(chrome.i18n.getMessage('error_credentialsNotReady'));
      }

      // Get export options
      this.updateLoadingMessage(
        chrome.i18n.getMessage('progress_configuringOptions')
      );
      const options = {
        includeMedia: this.includeMedia.checked,
        includeMetrics: this.includeMetrics.checked,
        expandUrls: this.expandUrls.checked,
        exportFormat: this.exportFormat.value,
        platform: 'twitter',
      };

      // Start export process
      this.updateLoadingMessage(
        chrome.i18n.getMessage('progress_startingExport')
      );

      // Show loading overlay on the page
      try {
        await chrome.tabs.sendMessage(this.currentTab.id, {
          action: 'show_loading_overlay',
          message: 'Preparing export...',
        });
      } catch (error) {
        // Could not show loading overlay on page
      }

      const response = await chrome.runtime.sendMessage({
        action: 'start_export',
        platform: 'twitter',
        tabId: this.currentTab.id,
        options: options,
      });

      if (response && response.success) {
        this.exportId = response.exportId;
        this.updateLoadingMessage(
          chrome.i18n.getMessage('progress_exportStarted')
        );
        this.showProgressInterface();
        this.startProgressPolling();
      } else {
        throw new Error(
          response?.error || chrome.i18n.getMessage('error_failedToStartExport')
        );
      }
    } catch (error) {
      // Export error

      // Hide loading overlay on error with multiple attempts
      await this.hideLoadingOverlayWithRetry();

      this.showError(
        chrome.i18n.getMessage('error_exportFailed') + error.message
      );
      this.resetExportButton();
    }
  }

  updateLoadingMessage(message) {
    // Update the button text to show current loading status
    const buttonTitle = this.exportButton.querySelector('.button-title');
    if (buttonTitle) {
      buttonTitle.textContent = message;
    }
  }

  async hideLoadingOverlayWithRetry(maxRetries = 3, retryDelay = 500) {
    // Try to hide loading overlay with multiple attempts and fallback methods
    let lastError = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // Primary method: Send message to content script
        await chrome.tabs.sendMessage(this.currentTab.id, {
          action: 'hide_loading_overlay',
        });

        // If we reach here, the message was sent successfully
        return;
      } catch (error) {
        lastError = error;
        console.warn(
          `Attempt ${attempt} to hide loading overlay failed:`,
          error
        );

        // Wait before retrying (except on last attempt)
        if (attempt < maxRetries) {
          await new Promise((resolve) => setTimeout(resolve, retryDelay));
        }
      }
    }

    // All attempts failed, try fallback methods
    console.error(
      'All attempts to hide loading overlay failed, trying fallback methods:',
      lastError
    );

    try {
      // Fallback 1: Try to inject a script to remove overlay
      await chrome.scripting.executeScript({
        target: { tabId: this.currentTab.id },
        func: () => {
          // Remove any loading overlays that might be stuck
          const overlays = document.querySelectorAll(
            '.abstract-bookmark-loading-overlay, .abstract-bookmark-progress-overlay'
          );
          overlays.forEach((overlay) => overlay.remove());
        },
      });
      console.log('Loading overlay removed using fallback script injection');
    } catch (scriptError) {
      console.error('Fallback script injection also failed:', scriptError);

      // Fallback 2: Send message to background script to handle cleanup
      try {
        await chrome.runtime.sendMessage({
          action: 'force_cleanup_loading_overlay',
          tabId: this.currentTab.id,
        });
        console.log('Requested background script to cleanup loading overlay');
      } catch (bgError) {
        console.error(
          'Background script cleanup request also failed:',
          bgError
        );
      }
    }
  }

  showProgressInterface() {
    this.exportButton.style.display = 'none';
    this.progressSection.style.display = 'block';
    this.progressText.textContent = chrome.i18n.getMessage(
      'progress_initializingExport'
    );
    this.progressPercentage.textContent = '0%';
    this.progressFill.style.width = '0%';
  }

  startProgressPolling() {
    this.progressFailureCount = 0;
    this.maxProgressFailures = 10; // Allow 10 consecutive failures before giving up

    this.progressInterval = setInterval(async () => {
      try {
        const response = await chrome.runtime.sendMessage({
          action: 'get_export_progress',
          exportId: this.exportId,
        });

        if (response && response.success) {
          this.progressFailureCount = 0; // Reset failure count on success
          this.updateProgress(response.progress);

          if (response.progress.status === 'completed') {
            this.handleExportComplete(response.progress);
          } else if (response.progress.status === 'error') {
            this.handleExportError(response.progress.error);
          } else if (response.progress.status === 'cancelled') {
            this.handleExportError('Export was cancelled');
          }
        } else {
          this.progressFailureCount++;
          console.warn(
            `Progress polling failed (${this.progressFailureCount}/${this.maxProgressFailures}):`,
            response
          );

          if (this.progressFailureCount >= this.maxProgressFailures) {
            console.error(
              'Too many progress polling failures, stopping export'
            );
            this.handleExportError('Export stopped responding');
          }
        }
      } catch (error) {
        this.progressFailureCount++;
        console.warn(
          `Progress polling error (${this.progressFailureCount}/${this.maxProgressFailures}):`,
          error
        );

        if (this.progressFailureCount >= this.maxProgressFailures) {
          console.error('Too many progress polling errors, stopping export');
          this.handleExportError('Export stopped responding');
        }
      }
    }, 1000);
  }

  updateProgress(progress) {
    const percentage = Math.round(progress.percentage || 0);
    this.progressFill.style.width = percentage + '%';
    this.progressPercentage.textContent = percentage + '%';

    if (progress.message) {
      this.progressText.textContent = progress.message;
    }
  }

  async handleExportComplete(progress) {
    clearInterval(this.progressInterval);
    this.exportInProgress = false;

    // Hide loading overlay on the page with multiple attempts
    await this.hideLoadingOverlayWithRetry();

    this.progressSection.style.display = 'none';
    this.showSuccess(
      chrome.i18n.getMessage('success_exportCompleted', [
        progress.totalBookmarks || 0,
      ])
    );

    // Reset after delay
    setTimeout(() => {
      this.resetInterface();
    }, 3000);
  }

  async handleExportError(error) {
    clearInterval(this.progressInterval);
    this.exportInProgress = false;

    // Hide loading overlay on the page with multiple attempts
    await this.hideLoadingOverlayWithRetry();

    this.progressSection.style.display = 'none';
    this.showError(
      chrome.i18n.getMessage('error_exportFailed') +
        (error || chrome.i18n.getMessage('error_unknownError'))
    );
    this.resetExportButton();
  }

  async cancelExport() {
    if (!this.exportInProgress || !this.exportId) return;

    try {
      await chrome.runtime.sendMessage({
        action: 'cancel_export',
        exportId: this.exportId,
      });

      // Hide loading overlay on the page with multiple attempts
      await this.hideLoadingOverlayWithRetry();

      clearInterval(this.progressInterval);
      this.exportInProgress = false;
      this.exportId = null;

      this.progressSection.style.display = 'none';
      this.showError(chrome.i18n.getMessage('error_exportCancelled'));
      this.resetExportButton();
    } catch (error) {
      // Cancel export error
    }
  }

  toggleOptions() {
    const isExpanded = this.optionsContent.style.display === 'block';
    this.optionsContent.style.display = isExpanded ? 'none' : 'block';
    this.optionsToggle.classList.toggle('expanded', !isExpanded);
  }

  showSuccess(message) {
    this.statusMessage.style.display = 'block';
    this.statusMessage.className = 'status-message success';
    this.statusIcon.textContent = '✅';
    this.statusText.textContent = message;
  }

  showError(message) {
    this.statusMessage.style.display = 'block';
    this.statusMessage.className = 'status-message error';
    this.statusIcon.textContent = '❌';
    this.statusText.textContent = message;
  }

  resetInterface() {
    this.statusMessage.style.display = 'none';
    this.resetExportButton();
  }

  resetExportButton() {
    this.exportButton.style.display = 'block';
    this.exportButton.classList.remove('loading');
    this.exportButton.disabled = false;
    this.progressSection.style.display = 'none';
    this.exportInProgress = false;
    this.exportId = null;

    // Reset button text to original
    const buttonTitle = this.exportButton.querySelector('.button-title');
    if (buttonTitle) {
      buttonTitle.textContent = chrome.i18n.getMessage(
        'popup_downloadAllBookmarks'
      );
    }
  }

  openSettings() {
    chrome.runtime.openOptionsPage();
  }

  openHelp() {
    chrome.tabs.create({
      url: 'https://github.com/abstractbookmark/help',
    });
  }

  handleMessage(message, sender, sendResponse) {
    switch (message.action) {
      case 'export_progress_update':
        if (message.exportId === this.exportId) {
          this.updateProgress(message.progress);
        }
        break;

      case 'export_completed':
        if (message.exportId === this.exportId) {
          this.handleExportComplete(message.progress);
        }
        break;

      case 'export_error':
        if (message.exportId === this.exportId) {
          this.handleExportError(message.error);
        }
        break;
    }
  }

  updateExportButtonText() {
    const format = this.exportFormat.value;
    const buttonSubtitle = this.exportButton.querySelector('.button-subtitle');

    // Helper function to get message using custom i18n or fallback
    const getMessage = (key) => {
      if (this.i18n && this.i18n.initialized) {
        return this.i18n.getMessage(key);
      } else {
        return chrome.i18n.getMessage(key);
      }
    };

    const formatDescriptions = {
      json: getMessage('export_formatJson'),
      excel: getMessage('export_formatExcel'),
      csv: getMessage('export_formatCsv'),
      html: getMessage('export_formatHtml'),
    };

    buttonSubtitle.textContent =
      formatDescriptions[format] || getMessage('popup_exportAsJson');
  }

  async handleQuickLanguageChange() {
    // handleQuickLanguageChange called
    const selectedLanguage = this.quickLanguageSelect.value;
    // Selected language

    try {
      // Update settings with new language and disable auto-detection
      // Getting current settings
      const { settings } = await chrome.storage.sync.get('settings');
      // Current settings

      const updatedSettings = {
        ...settings,
        language: selectedLanguage,
        languageAutoDetected: false, // Disable auto-detection when user manually selects
      };
      // Updated settings

      // Saving settings to storage
      await chrome.storage.sync.set({ settings: updatedSettings });
      // Settings saved successfully

      // Show brief feedback
      this.quickLanguageSelect.title = 'Language changed!';

      // Notify background script about language change
      // Sending message to background script
      chrome.runtime.sendMessage({
        action: 'language_changed',
        language: selectedLanguage,
      });

      // Reinitialize i18n with new language and update UI immediately
      // Reinitializing i18n
      await this.i18n.init();
      // Updating UI elements
      this.updateAllI18nElements();
      this.updateExportButtonText(); // Also update export button text
      // Language change completed successfully

      // Reset title after a moment
      setTimeout(() => {
        this.quickLanguageSelect.title = 'Quick Language Change';
      }, 2000);
    } catch (error) {
      // Failed to change language
      // Reset selector on error
      this.loadCurrentLanguage();
    }
  }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new PopupController();
});
