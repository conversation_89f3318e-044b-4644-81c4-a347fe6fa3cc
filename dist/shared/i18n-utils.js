/**
 * i18n Utility Functions for AbstractBookmark Extension
 * Provides helper functions for internationalization using Chrome Extension i18n API
 */

export class I18nUtils {
  /**
   * Get a localized message by key
   * @param {string} key - The message key
   * @param {string|string[]} substitutions - Optional substitutions for placeholders
   * @returns {string} The localized message
   */
  static getMessage(key, substitutions = null) {
    try {
      if (substitutions) {
        return chrome.i18n.getMessage(key, substitutions);
      }
      return chrome.i18n.getMessage(key);
    } catch (error) {
      console.warn(`i18n: Failed to get message for key "${key}":`, error);
      return key; // Fallback to key if message not found
    }
  }

  /**
   * Get current UI language
   * @returns {string} Current language code (e.g., 'en', 'tr')
   */
  static getCurrentLanguage() {
    try {
      return chrome.i18n.getUILanguage();
    } catch (error) {
      console.warn('i18n: Failed to get current language:', error);
      return 'en'; // Fallback to English
    }
  }

  /**
   * Get accept languages from browser
   * @returns {Promise<string[]>} Array of language codes
   */
  static async getAcceptLanguages() {
    try {
      return new Promise((resolve) => {
        chrome.i18n.getAcceptLanguages((languages) => {
          resolve(languages);
        });
      });
    } catch (error) {
      console.warn('i18n: Failed to get accept languages:', error);
      return ['en']; // Fallback to English
    }
  }

  /**
   * Detect best matching language from available languages
   * @param {string[]} availableLanguages - Array of available language codes
   * @returns {Promise<string>} Best matching language code
   */
  static async detectBestLanguage(availableLanguages = []) {
    try {
      const acceptLanguages = await this.getAcceptLanguages();
      const currentLanguage = this.getCurrentLanguage();

      // First check current UI language
      if (availableLanguages.includes(currentLanguage)) {
        return currentLanguage;
      }

      // Check accept languages in order
      for (const lang of acceptLanguages) {
        // Check exact match
        if (availableLanguages.includes(lang)) {
          return lang;
        }

        // Check language without region (e.g., 'en' from 'en-US')
        const baseLang = lang.split('-')[0];
        if (availableLanguages.includes(baseLang)) {
          return baseLang;
        }
      }

      // Fallback to English if available, otherwise first available language
      return availableLanguages.includes('en')
        ? 'en'
        : availableLanguages[0] || 'en';
    } catch (error) {
      console.warn('i18n: Failed to detect best language:', error);
      return 'en';
    }
  }

  /**
   * Format a message with placeholders
   * @param {string} key - The message key
   * @param {Object} placeholders - Object with placeholder values
   * @returns {string} Formatted message
   */
  static formatMessage(key, placeholders = {}) {
    try {
      let message = this.getMessage(key);

      // Replace placeholders in format $PLACEHOLDER$
      Object.keys(placeholders).forEach((placeholder) => {
        const pattern = new RegExp(`\\$${placeholder.toUpperCase()}\\$`, 'g');
        message = message.replace(pattern, placeholders[placeholder]);
      });

      return message;
    } catch (error) {
      console.warn(`i18n: Failed to format message for key "${key}":`, error);
      return key;
    }
  }

  /**
   * Update element text content with localized message
   * @param {HTMLElement} element - The element to update
   * @param {string} key - The message key
   * @param {string|string[]} substitutions - Optional substitutions
   */
  static updateElementText(element, key, substitutions = null) {
    if (!element) return;

    try {
      element.textContent = this.getMessage(key, substitutions);
    } catch (error) {
      console.warn(
        `i18n: Failed to update element text for key "${key}":`,
        error
      );
    }
  }

  /**
   * Update element innerHTML with localized message (for HTML content)
   * @param {HTMLElement} element - The element to update
   * @param {string} key - The message key
   * @param {string|string[]} substitutions - Optional substitutions
   */
  static updateElementHTML(element, key, substitutions = null) {
    if (!element) return;

    try {
      element.innerHTML = this.getMessage(key, substitutions);
    } catch (error) {
      console.warn(
        `i18n: Failed to update element HTML for key "${key}":`,
        error
      );
    }
  }

  /**
   * Update multiple elements with localized messages
   * @param {Object} elementMap - Map of element selectors to message keys
   * @param {HTMLElement} container - Container element (default: document)
   */
  static updateElements(elementMap, container = document) {
    Object.keys(elementMap).forEach((selector) => {
      const element = container.querySelector(selector);
      if (element) {
        const config = elementMap[selector];
        if (typeof config === 'string') {
          this.updateElementText(element, config);
        } else {
          const { key, substitutions, useHTML = false } = config;
          if (useHTML) {
            this.updateElementHTML(element, key, substitutions);
          } else {
            this.updateElementText(element, key, substitutions);
          }
        }
      }
    });
  }

  /**
   * Get available languages list
   * @returns {Array} Array of language objects with code and name
   */
  static getAvailableLanguages() {
    return [
      { code: 'en', name: 'English' },
      { code: 'tr', name: 'Türkçe' },
      { code: 'es', name: 'Español' },
      { code: 'fr', name: 'Français' },
      { code: 'de', name: 'Deutsch' },
      { code: 'zh-CN', name: '中文 (简体)' },
      { code: 'ja', name: '日本語' },
      { code: 'ru', name: 'Русский' },
      { code: 'hi', name: 'हिन्दी' },
      { code: 'pt', name: 'Português' },
      { code: 'bn', name: 'বাংলা' },
      { code: 'vi', name: 'Tiếng Việt' },
      { code: 'th', name: 'ไทย' },
    ];
  }

  /**
   * Get language name by code
   * @param {string} code - Language code
   * @returns {string} Language name
   */
  static getLanguageName(code) {
    const language = this.getAvailableLanguages().find(
      (lang) => lang.code === code
    );
    return language ? language.name : code;
  }
}

// Make available globally
window.I18nUtils = I18nUtils;
