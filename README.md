# AbstractBookmark Chrome Extension

Advanced bookmark management and export tool for social media platforms with simplified single-button interface.

## 🚀 Features

- **Twitter/X Bookmark Export**: Complete bookmark export with one click
- **Simplified Interface**: Clean, minimal popup with single "Download All Bookmarks" button
- **Advanced Export**: JSON format with rich metadata including media, metrics, and URLs
- **Real-time Progress**: Live progress feedback during export operations
- **Smart Processing**: Handles large bookmark collections with pagination and rate limiting
- **Secure Authentication**: Uses your existing Twitter session without storing credentials
- **Modern UI**: Responsive design following modern UI/UX principles

## 🎯 Current Status: Epic 2 Complete

This extension implements the same technical approach as analyzed Twitter bookmark export extensions:
- ✅ GraphQL API integration (bookmark_timeline_v2 endpoint)
- ✅ Credential extraction from active Twitter session
- ✅ Rate limiting and error handling
- ✅ Cursor-based pagination for large datasets
- ✅ Comprehensive data processing pipeline
- ✅ Real-time progress feedback

## 🏗️ Architecture

Built with modern Chrome Extension Manifest v3 standards:

- **Service Worker**: Background processing and API management
- **Content Scripts**: Platform-specific UI injection and interaction
- **Modular Design**: Clean separation of concerns
- **TypeScript Support**: Type-safe development
- **Comprehensive Testing**: Unit, integration, and E2E tests

## 📋 Development Setup

### Prerequisites

- Node.js 18+ and npm
- Chrome/Chromium browser for testing

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd abstractBookmark

# Install dependencies
npm install

# Build for development
npm run build:dev

# Start development with hot reload
npm run dev
```

### Testing

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

### Code Quality

```bash
# Lint code
npm run lint

# Fix linting issues
npm run lint:fix

# Format code
npm run format

# Validate everything
npm run validate
```

## 🔧 Project Structure

```
abstractBookmark/
├── manifest.json              # Extension manifest
├── background/                # Service worker and modules
│   ├── service-worker.js     # Main service worker
│   └── modules/              # Core business logic
├── content/                  # Content scripts
│   ├── platforms/           # Platform-specific scripts
│   └── shared/              # Shared utilities
├── popup/                   # Extension popup UI
├── options/                 # Settings and configuration
├── assets/                  # Static assets
├── tests/                   # Test files
└── docs/                    # Documentation
```

## 🎯 Development Framework

This project follows a structured epic-based development approach:

### Epic 1: Core Architecture Foundation ✅
- [x] Project structure setup
- [x] Build tools configuration
- [x] Service worker implementation
- [x] Core modules structure
- [x] Testing framework

### Epic 2: Twitter Integration System (In Progress)
- [ ] Twitter API manager
- [ ] Content script injection
- [ ] Bookmark data processing
- [ ] Pagination system
- [ ] Integration testing

### Epic 3: Multi-Platform Support (Planned)
- [ ] Platform abstraction layer
- [ ] LinkedIn integration
- [ ] Instagram integration
- [ ] Reddit integration

### Epic 4: Advanced Export System (Planned)
- [ ] Multiple export formats
- [ ] Export configuration
- [ ] Batch processing
- [ ] Data validation

### Epic 5: User Interface & Experience (Planned)
- [ ] Popup interface
- [ ] Options page
- [ ] Progress notifications
- [ ] Responsive design

## 📊 Quality Standards

- **Code Coverage**: 90%+ required
- **Performance**: <50MB memory, <2s API response
- **Security**: CSRF protection, input validation, encrypted storage
- **Accessibility**: WCAG 2.1 AA compliance
- **Browser Support**: Chrome 88+, Edge 88+, Opera 74+

## 🔒 Security

- No hardcoded credentials
- CSRF token validation
- Content Security Policy enforcement
- Input sanitization
- Encrypted sensitive data storage

## 📝 Contributing

1. Follow the established coding standards
2. Write tests for new functionality
3. Ensure all quality gates pass
4. Update documentation as needed

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

For issues and feature requests, please use the GitHub issue tracker.

---

**Current Status**: Epic 1 Complete - Core Architecture Foundation ✅  
**Next Milestone**: Epic 2 - Twitter Integration System
