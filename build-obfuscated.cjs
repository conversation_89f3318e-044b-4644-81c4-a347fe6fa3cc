// This script bundles, minifies and obfuscates your extension JS files for production.
// Usage: node build-obfuscated.cjs
const path = require('path');
const fs = require('fs');
const glob = require('glob');
const webpack = require('webpack');
const TerserPlugin = require('terser-webpack-plugin');
const JavaScriptObfuscator = require('javascript-obfuscator');
const fse = require('fs-extra');
const SRC_DIR = __dirname;
const DIST_DIR = path.resolve(__dirname, 'dist-obfuscated');
console.log('🚀 Starting obfuscated build process...');
// Obfuscation options
const obfuscatorOptions = {
  compact: true,
  controlFlowFlattening: true,
  controlFlowFlatteningThreshold: 0.75,
  deadCodeInjection: true,
  deadCodeInjectionThreshold: 0.4,
  debugProtection: false, // Disable for extension compatibility
  debugProtectionInterval: 0,
  disableConsoleOutput: false, // Keep console for debugging
  identifierNamesGenerator: 'hexadecimal',
  log: false,
  numbersToExpressions: true,
  renameGlobals: false, // Important for extensions
  rotateStringArray: true,
  selfDefending: false, // Disable for extension compatibility
  shuffleStringArray: true,
  simplify: true,
  splitStrings: true,
  splitStringsChunkLength: 10,
  stringArray: true,
  stringArrayCallsTransform: true,
  stringArrayEncoding: ['base64'],
  stringArrayIndexShift: true,
  stringArrayRotate: true,
  stringArrayShuffle: true,
  stringArrayWrappersCount: 2,
  stringArrayWrappersChainedCalls: true,
  stringArrayWrappersParametersMaxCount: 4,
  stringArrayWrappersType: 'function',
  stringArrayThreshold: 0.75,
  transformObjectKeys: true,
  unicodeEscapeSequence: false,
};
// Entry points for webpack
const entries = {
  'background/service-worker': {
    entry: path.join(SRC_DIR, 'background/service-worker.js'),
    target: 'webworker',
  },
  'content/platforms/twitter-injector': {
    entry: path.join(SRC_DIR, 'content/platforms/twitter-injector.js'),
    target: 'web',
  },
  'content/platforms/twitter-api': {
    entry: path.join(SRC_DIR, 'content/platforms/twitter-api.js'),
    target: 'web',
  },
  'popup/popup': {
    entry: path.join(SRC_DIR, 'popup/popup.js'),
    target: 'web',
  },
  'options/options': {
    entry: path.join(SRC_DIR, 'options/options.js'),
    target: 'web',
  },
};
// Clean and create dist directory
console.log('🧹 Cleaning dist directory...');
fse.removeSync(DIST_DIR);
fse.ensureDirSync(DIST_DIR);
// Function to obfuscate a JavaScript file
async function obfuscateFile(filePath) {
  try {
    const code = fs.readFileSync(filePath, 'utf8');
    const obfuscated = JavaScriptObfuscator.obfuscate(code, obfuscatorOptions);
    fs.writeFileSync(filePath, obfuscated.getObfuscatedCode());
    console.log(`✅ Obfuscated: ${path.relative(DIST_DIR, filePath)}`);
  } catch (error) {
    console.error(`❌ Failed to obfuscate ${filePath}:`, error.message);
  }
}
// Function to minify HTML files
function minifyHtml(content) {
  return content
    .replace(/\s+/g, ' ')
    .replace(/>\s+</g, '><')
    .replace(/\s+>/g, '>')
    .replace(/<\s+/g, '<')
    .trim();
}
// Function to minify CSS files
function minifyCss(content) {
  return content
    .replace(/\/\*[\s\S]*?\*\//g, '')
    .replace(/\s+/g, ' ')
    .replace(/;\s*}/g, '}')
    .replace(/\s*{\s*/g, '{')
    .replace(/;\s*/g, ';')
    .replace(/:\s*/g, ':')
    .trim();
}
// Build each entry with webpack and then obfuscate
async function buildAndObfuscate() {
  const buildPromises = Object.entries(entries).map(
    ([name, { entry, target }]) => {
      return new Promise((resolve, reject) => {
        const singleConfig = {
          mode: 'production',
          entry: { [name]: entry },
          output: {
            path: DIST_DIR,
            filename: '[name].js',
          },
          target,
          optimization: {
            minimize: true,
            minimizer: [
              new TerserPlugin({
                extractComments: false,
                terserOptions: {
                  compress: {
                    drop_console: false, // Keep console for debugging
                    drop_debugger: true,
                  },
                  mangle: {
                    reserved: ['chrome', 'browser'], // Preserve extension APIs
                  },
                },
              }),
            ],
          },
          module: {
            rules: [
              {
                test: /\.js$/,
                exclude: /node_modules/,
                use: {
                  loader: 'babel-loader',
                  options: {
                    presets: ['@babel/preset-env'],
                  },
                },
              },
            ],
          },
          resolve: { extensions: ['.js'] },
        };
        webpack(singleConfig, async (err, stats) => {
          if (err || stats.hasErrors()) {
            console.error(
              `❌ Webpack build failed for ${name}:`,
              err || stats.toString()
            );
            reject(err || new Error(stats.toString()));
            return;
          }
          console.log(`✅ Webpack build complete for ${name}`);
          // Obfuscate the built file
          const builtFilePath = path.join(DIST_DIR, `${name}.js`);
          if (fs.existsSync(builtFilePath)) {
            await obfuscateFile(builtFilePath);
          }
          resolve();
        });
      });
    }
  );
  await Promise.all(buildPromises);
}
// Copy and process static files
async function copyAndProcessFiles() {
  console.log('📁 Copying and processing static files...');
  const copyList = [
    'manifest.json',
    '_locales',
    'assets',
    'aphedradoc',
    'privacy_policy_en.md',
    'PRIVACY_POLICY.md',
    'README.md',
  ];
  // Copy basic files
  copyList.forEach((item) => {
    const srcPath = path.join(SRC_DIR, item);
    const destPath = path.join(DIST_DIR, item);
    if (fs.existsSync(srcPath)) {
      fse.copySync(srcPath, destPath);
      console.log(`✅ Copied: ${item}`);
    }
  });
  // Copy and minify HTML/CSS files
  const htmlCssFiles = ['popup', 'options'];
  htmlCssFiles.forEach((dir) => {
    const srcDir = path.join(SRC_DIR, dir);
    const destDir = path.join(DIST_DIR, dir);
    if (fs.existsSync(srcDir)) {
      fse.ensureDirSync(destDir);
      // Process each file in the directory
      const files = fs.readdirSync(srcDir);
      files.forEach((file) => {
        const srcFile = path.join(srcDir, file);
        const destFile = path.join(destDir, file);
        if (file.endsWith('.html')) {
          const content = fs.readFileSync(srcFile, 'utf8');
          const minified = minifyHtml(content);
          fs.writeFileSync(destFile, minified);
          console.log(`✅ Minified HTML: ${dir}/${file}`);
        } else if (file.endsWith('.css')) {
          const content = fs.readFileSync(srcFile, 'utf8');
          const minified = minifyCss(content);
          fs.writeFileSync(destFile, minified);
          console.log(`✅ Minified CSS: ${dir}/${file}`);
        } else if (!file.endsWith('.js')) {
          // Copy other files (not JS, as they're handled by webpack)
          fse.copySync(srcFile, destFile);
          console.log(`✅ Copied: ${dir}/${file}`);
        }
      });
    }
  });
  // Copy background and content directories (excluding JS files)
  ['background', 'content'].forEach((dir) => {
    const srcDir = path.join(SRC_DIR, dir);
    const destDir = path.join(DIST_DIR, dir);
    if (fs.existsSync(srcDir)) {
      fse.ensureDirSync(destDir);
      // Copy directory structure but skip JS files (they're handled by webpack)
      fse.copySync(srcDir, destDir, {
        filter: (src) => {
          return !src.endsWith('.js') || src.includes('modules');
        },
      });
      console.log(`✅ Copied directory: ${dir}`);
    }
  });
  // Copy shared directory
  const sharedSrc = path.join(SRC_DIR, 'shared');
  const sharedDest = path.join(DIST_DIR, 'shared');
  if (fs.existsSync(sharedSrc)) {
    fse.copySync(sharedSrc, sharedDest);
    console.log('✅ Copied: shared');
  }
  // Copy icons to root level
  const iconsSrc = path.join(SRC_DIR, 'assets/icons');
  const iconsDest = path.join(DIST_DIR, 'icons');
  if (fs.existsSync(iconsSrc)) {
    fse.copySync(iconsSrc, iconsDest);
    console.log('✅ Copied: icons');
  }
}
// Main build process
async function main() {
  try {
    console.log('🚀 Starting obfuscated build process...');
    // Step 1: Build and obfuscate JavaScript files
    await buildAndObfuscate();
    // Step 2: Copy and process static files
    await copyAndProcessFiles();
    console.log('🎉 Obfuscated build completed successfully!');
    console.log(`📦 Output directory: ${DIST_DIR}`);
  } catch (error) {
    console.error('❌ Build failed:', error);
    process.exit(1);
  }
}
// Run the build
main();
