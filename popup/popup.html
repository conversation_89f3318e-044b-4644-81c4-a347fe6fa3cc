<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AbstractBookmark</title>
    <link rel="stylesheet" href="popup.css" />
  </head>
  <body>
    <div class="popup-container">
      <!-- Header -->
      <header class="popup-header">
        <div class="logo-container">
          <img
            src="../assets/icons/aphedra-logo.png"
            alt="Aphedra"
            class="aphedra-logo"
          />
          <div class="brand-separator"></div>
          <img src="../icons/icon32.png" alt="AbstractBookmark" class="logo" />
          <h1 class="app-title" data-i18n="appName">AbstractBookmark</h1>
        </div>
        <div class="platform-indicator" id="platformIndicator">
          <span
            class="platform-status"
            id="platformStatus"
            data-i18n="popup_detectingPlatform"
            >Detecting platform...</span
          >
        </div>
      </header>

      <!-- Main Content -->
      <main class="popup-main">
        <!-- Platform Detection Message -->
        <div class="platform-message" id="platformMessage">
          <div class="message-content">
            <div class="message-icon">📚</div>
            <p class="message-text" data-i18n="popup_navigateToBookmarks">
              Navigate to your Twitter/X bookmarks page to export your saved
              tweets.
            </p>
            <a
              href="https://x.com/i/bookmarks"
              target="_blank"
              class="bookmark-link"
              data-i18n="popup_openTwitterBookmarks"
            >
              Open Twitter Bookmarks →
            </a>
          </div>
        </div>

        <!-- Export Section (shown when on supported platform) -->
        <div class="export-section" id="exportSection" style="display: none">
          <div class="export-info">
            <div class="bookmark-count" id="bookmarkCount">
              <span class="count-number">--</span>
              <span class="count-label" data-i18n="popup_bookmarksDetected"
                >bookmarks detected</span
              >
            </div>
          </div>

          <!-- Primary Export Button -->
          <button class="export-button primary" id="exportButton">
            <div class="button-content">
              <div class="button-icon">⬇️</div>
              <div class="button-text">
                <span
                  class="button-title"
                  data-i18n="popup_downloadAllBookmarks"
                  >Download All Bookmarks</span
                >
                <span class="button-subtitle" data-i18n="popup_exportAsJson"
                  >Export as JSON with full metadata</span
                >
              </div>
            </div>
            <div class="button-loader" id="buttonLoader">
              <div class="loader-spinner"></div>
            </div>
          </button>

          <!-- Progress Section -->
          <div
            class="progress-section"
            id="progressSection"
            style="display: none"
          >
            <div class="progress-bar">
              <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-info">
              <span
                class="progress-text"
                id="progressText"
                data-i18n="popup_preparingExport"
                >Preparing export...</span
              >
              <span class="progress-percentage" id="progressPercentage"
                >0%</span
              >
            </div>
            <button
              class="cancel-button"
              id="cancelButton"
              data-i18n="popup_cancel"
            >
              Cancel
            </button>
          </div>

          <!-- Export Options (expanded by default) -->
          <div class="export-options" id="exportOptions">
            <button class="options-toggle expanded" id="optionsToggle">
              <span data-i18n="popup_exportOptions">Export Options</span>
              <span class="toggle-icon">▼</span>
            </button>
            <div
              class="options-content"
              id="optionsContent"
              style="display: block"
            >
              <div class="option-group">
                <label class="option-label" data-i18n="popup_exportFormat">
                  Export Format:
                </label>
                <select id="exportFormat" class="format-select">
                  <option value="json" data-i18n="popup_formatJson">
                    JSON - Full metadata
                  </option>
                  <option value="excel" data-i18n="popup_formatExcel">
                    Excel - Spreadsheet format
                  </option>
                  <option value="csv" data-i18n="popup_formatCsv">
                    CSV - Comma separated
                  </option>
                  <option value="html" data-i18n="popup_formatHtml">
                    HTML - Web page
                  </option>
                </select>
              </div>
              <div class="option-group">
                <label class="option-label">
                  <input type="checkbox" id="includeMedia" checked />
                  <span class="checkmark"></span>
                  <span data-i18n="popup_includeMedia"
                    >Include media URLs and metadata</span
                  >
                </label>
              </div>
              <div class="option-group">
                <label class="option-label">
                  <input type="checkbox" id="includeMetrics" checked />
                  <span class="checkmark"></span>
                  <span data-i18n="popup_includeMetrics"
                    >Include engagement metrics</span
                  >
                </label>
              </div>
              <div class="option-group">
                <label class="option-label">
                  <input type="checkbox" id="expandUrls" checked />
                  <span class="checkmark"></span>
                  <span data-i18n="popup_expandUrls"
                    >Expand shortened URLs</span
                  >
                </label>
              </div>
            </div>
          </div>
        </div>

        <!-- Status Messages -->
        <div class="status-message" id="statusMessage" style="display: none">
          <div class="status-content">
            <div class="status-icon" id="statusIcon">✅</div>
            <div
              class="status-text"
              id="statusText"
              data-i18n="popup_exportCompleted"
            >
              Export completed successfully!
            </div>
          </div>
        </div>
      </main>

      <!-- Footer -->
      <footer class="popup-footer">
        <div class="footer-links">
          <button
            class="footer-link"
            id="settingsButton"
            data-i18n="popup_settings"
          >
            Settings
          </button>
          <button class="footer-link" id="helpButton" data-i18n="popup_help">
            Help
          </button>
          <select
            class="language-selector"
            id="quickLanguageSelect"
            title="Quick Language Change"
          >
            <option value="en">🇺🇸 EN</option>
            <option value="tr">🇹🇷 TR</option>
            <option value="es">🇪🇸 ES</option>
            <option value="fr">🇫🇷 FR</option>
            <option value="de">🇩🇪 DE</option>
            <option value="zh-CN">🇨🇳 中文</option>
            <option value="ja">🇯🇵 日本語</option>
            <option value="ko">🇰🇷 한국어</option>
            <option value="ru">🇷🇺 RU</option>
            <option value="hi">🇮🇳 हिन्दी</option>
            <option value="pt">🇵🇹 PT</option>
            <option value="bn">🇧🇩 বাংলা</option>
            <option value="vi">🇻🇳 VI</option>
            <option value="th">🇹🇭 ไทย</option>
          </select>
        </div>
        <div class="version-info">v1.0.0</div>
      </footer>
    </div>

    <script src="../shared/content-i18n.js"></script>
    <script src="popup.js"></script>
  </body>
</html>
