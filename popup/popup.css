/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #1a1a1a;
  background: #ffffff;
  width: 380px;
  min-height: 500px;
}

/* Popup Container */
.popup-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  min-height: 500px;
}

/* Header */
.popup-header {
  padding: 16px 20px 12px;
  border-bottom: 1px solid #e1e8ed;
  background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
  color: white;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.logo {
  width: 24px;
  height: 24px;
  border-radius: 4px;
}

.aphedra-logo {
  width: 28px;
  height: 28px;
  border-radius: 6px;
  object-fit: contain;
}

.brand-separator {
  width: 1px;
  height: 20px;
  background: rgba(255, 255, 255, 0.3);
  margin: 0 4px;
}

.app-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.platform-indicator {
  font-size: 12px;
  opacity: 0.9;
}

.platform-status {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.platform-status::before {
  content: '●';
  color: #ffd700;
  animation: pulse 2s infinite;
}

.platform-status.supported::before {
  color: #4ade80;
  animation: none;
}

.platform-status.unsupported::before {
  color: #f87171;
  animation: none;
}

/* Main Content */
.popup-main {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Platform Message */
.platform-message {
  text-align: center;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.message-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.message-icon {
  font-size: 32px;
  margin-bottom: 4px;
}

.message-text {
  color: #64748b;
  margin: 0;
  line-height: 1.6;
}

.bookmark-link {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  color: #1e40af;
  text-decoration: none;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 8px;
  background: #f1f5f9;
  transition: all 0.2s ease;
}

.bookmark-link:hover {
  background: #e2e8f0;
  transform: translateY(-1px);
}

/* Export Section */
.export-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.export-info {
  text-align: center;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #cbd5e1;
}

.bookmark-count {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.count-number {
  font-size: 24px;
  font-weight: 700;
  color: #1e40af;
}

.count-label {
  font-size: 12px;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Primary Export Button */
.export-button {
  position: relative;
  width: 100%;
  padding: 16px 20px;
  margin: 12px 0;
  background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
  color: white;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);
  overflow: hidden;
}

.export-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(30, 64, 175, 0.4);
}

.export-button:active {
  transform: translateY(0);
}

.export-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 8px rgba(30, 64, 175, 0.2);
}

.button-content {
  display: flex;
  align-items: center;
  gap: 12px;
  justify-content: center;
}

.button-icon {
  font-size: 18px;
}

.button-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
}

.button-title {
  font-size: 16px;
  font-weight: 600;
}

.button-subtitle {
  font-size: 12px;
  opacity: 0.9;
  font-weight: 400;
}

.button-loader {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(30, 64, 175, 0.9);
  display: none;
  align-items: center;
  justify-content: center;
}

.export-button.loading .button-content {
  opacity: 0.8;
}

.export-button.loading .button-subtitle {
  display: none;
}

.export-button.loading .button-loader {
  display: flex;
}

.loader-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Progress Section */
.progress-section {
  padding: 16px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 12px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1e40af, #1e3a8a);
  border-radius: 4px;
  transition: width 0.3s ease;
  width: 0%;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 13px;
}

.progress-text {
  color: #64748b;
}

.progress-percentage {
  font-weight: 600;
  color: #1e40af;
}

.cancel-button {
  width: 100%;
  padding: 8px 16px;
  background: #f1f5f9;
  color: #64748b;
  border: 1px solid #cbd5e1;
  border-radius: 8px;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.2s ease;
}

.cancel-button:hover {
  background: #e2e8f0;
  color: #475569;
}

/* Export Options */
.export-options {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.options-toggle {
  width: 100%;
  padding: 12px 16px;
  background: #f8fafc;
  border: none;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
  color: #64748b;
  transition: background 0.2s ease;
}

.options-toggle:hover {
  background: #f1f5f9;
}

.toggle-icon {
  transition: transform 0.2s ease;
}

.options-toggle.expanded .toggle-icon {
  transform: rotate(180deg);
}

.options-content {
  padding: 16px;
  background: white;
  border-top: 1px solid #e2e8f0;
}

.option-group {
  margin-bottom: 12px;
}

.option-group:last-child {
  margin-bottom: 0;
}

.format-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  color: #374151;
  margin-top: 4px;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.format-select:focus {
  outline: none;
  border-color: #1e40af;
  box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

.format-select:hover {
  border-color: #9ca3af;
}

.option-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 13px;
  color: #374151;
}

.option-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 16px;
  height: 16px;
  border: 2px solid #d1d5db;
  border-radius: 3px;
  position: relative;
  transition: all 0.2s ease;
}

.option-label input[type="checkbox"]:checked + .checkmark {
  background: #1e40af;
  border-color: #1e40af;
}

.option-label input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: -2px;
  left: 2px;
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* Status Message */
.status-message {
  padding: 16px;
  border-radius: 12px;
  text-align: center;
}

.status-message.success {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  color: #166534;
}

.status-message.error {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
}

.status-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.status-icon {
  font-size: 24px;
}

.status-text {
  font-weight: 500;
}

/* Footer */
.popup-footer {
  padding: 12px 20px;
  border-top: 1px solid #e1e8ed;
  background: #f8fafc;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-links {
  display: flex;
  gap: 16px;
}

.footer-link {
  background: none;
  border: none;
  color: #64748b;
  cursor: pointer;
  font-size: 12px;
  text-decoration: underline;
  transition: color 0.2s ease;
}

.footer-link:hover {
  color: #1e40af;
}

.version-info {
  font-size: 11px;
  color: #94a3b8;
}

/* Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Language Selector */
.language-selector {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 4px 8px;
  font-size: 12px;
  color: #475569;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 70px;
}

.language-selector:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.language-selector:focus {
  outline: none;
  border-color: #1e40af;
  box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

.footer-links {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Responsive adjustments */
@media (max-height: 600px) {
  .popup-main {
    padding: 16px;
    gap: 12px;
  }

  .export-button {
    padding: 14px 18px;
  }
}
