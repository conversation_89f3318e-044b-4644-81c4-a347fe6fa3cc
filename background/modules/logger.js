/**
 * Logger Module - Centralized logging system for AbstractBookmark
 */

export class Logger {
  constructor(context = 'AbstractBookmark') {
    this.context = context;
    this.logLevel = this.getLogLevel();
    this.maxLogEntries = 1000;
    this.logBuffer = [];
  }

  static LOG_LEVELS = {
    ERROR: 0,
    WARN: 1,
    INFO: 2,
    DEBUG: 3,
  };

  getLogLevel() {
    // In production, only show errors and warnings
    // In development, show all logs
    // Check if we're in development mode (extension ID contains 'unpacked')
    const isDevelopment =
      chrome.runtime.id && chrome.runtime.id.includes('unpacked');
    return isDevelopment ? Logger.LOG_LEVELS.DEBUG : Logger.LOG_LEVELS.WARN;
  }

  formatMessage(level, message, ...args) {
    const timestamp = new Date().toISOString();
    const levelStr = Object.keys(Logger.LOG_LEVELS)[level];
    return {
      timestamp,
      level: levelStr,
      context: this.context,
      message,
      args: args.length > 0 ? args : undefined,
    };
  }

  log(level, message, ...args) {
    if (level > this.logLevel) return;

    const logEntry = this.formatMessage(level, message, ...args);

    // Add to buffer for debugging
    this.logBuffer.push(logEntry);
    if (this.logBuffer.length > this.maxLogEntries) {
      this.logBuffer.shift();
    }

    // Console output disabled
    // const consoleMessage = `[${logEntry.timestamp}] [${logEntry.level}] [${logEntry.context}] ${message}`;

    // Console logging disabled for production
    // switch (level) {
    //   case Logger.LOG_LEVELS.ERROR:
    //     console.error(consoleMessage, ...args);
    //     break;
    //   case Logger.LOG_LEVELS.WARN:
    //     console.warn(consoleMessage, ...args);
    //     break;
    //   case Logger.LOG_LEVELS.INFO:
    //     console.info(consoleMessage, ...args);
    //     break;
    //   case Logger.LOG_LEVELS.DEBUG:
    //     console.debug(consoleMessage, ...args);
    //     break;
    // }

    // Store critical errors for user feedback
    if (level === Logger.LOG_LEVELS.ERROR) {
      this.storeError(logEntry);
    }
  }

  error(message, ...args) {
    this.log(Logger.LOG_LEVELS.ERROR, message, ...args);
  }

  warn(message, ...args) {
    this.log(Logger.LOG_LEVELS.WARN, message, ...args);
  }

  info(message, ...args) {
    this.log(Logger.LOG_LEVELS.INFO, message, ...args);
  }

  debug(message, ...args) {
    this.log(Logger.LOG_LEVELS.DEBUG, message, ...args);
  }

  async storeError(logEntry) {
    try {
      const { errors = [] } = await chrome.storage.local.get('errors');
      errors.push(logEntry);

      // Keep only last 50 errors
      if (errors.length > 50) {
        errors.splice(0, errors.length - 50);
      }

      await chrome.storage.local.set({ errors });
    } catch (error) {
      // Failed to store error log
    }
  }

  async getStoredErrors() {
    try {
      const { errors = [] } = await chrome.storage.local.get('errors');
      return errors;
    } catch (error) {
      // Failed to retrieve stored errors
      return [];
    }
  }

  async clearStoredErrors() {
    try {
      await chrome.storage.local.remove('errors');
    } catch (error) {
      // Failed to clear stored errors
    }
  }

  getLogBuffer() {
    return [...this.logBuffer];
  }

  clearLogBuffer() {
    this.logBuffer = [];
  }

  // Performance timing utilities
  time(label) {
    console.time(`[${this.context}] ${label}`);
  }

  timeEnd(label) {
    console.timeEnd(`[${this.context}] ${label}`);
  }

  // Group logging for related operations
  group(label) {
    console.group(`[${this.context}] ${label}`);
  }

  groupEnd() {
    console.groupEnd();
  }

  // Table logging for structured data
  table(data, columns) {
    console.table(data, columns);
  }
}
