/**
 * API Manager - Handles API interactions with different social media platforms
 */
import { Logger } from './logger.js';
export class APIManager {
  constructor() {
    this.logger = new Logger('APIManager');
    this.platforms = new Map();
    this.initializePlatforms();
  }
  initializePlatforms() {
    // Register supported platforms
    this.platforms.set('twitter', {
      name: 'Twitter/X',
      domain: 'twitter.com',
      alternativeDomains: ['x.com'],
      enabled: true,
      apiClass: 'TwitterAPI', // Will be implemented
    });
  }
  isPlatformSupported(platform) {
    const platformConfig = this.platforms.get(platform);
    return platformConfig && platformConfig.enabled;
  }
  getSupportedPlatforms() {
    return Array.from(this.platforms.entries())
      .filter(([, config]) => config.enabled)
      .map(([key, config]) => ({
        key,
        name: config.name,
        domain: config.domain,
        alternativeDomains: config.alternativeDomains,
      }));
  }
  async fetchBookmarks(platform, options, progressCallback) {
    if (!this.isPlatformSupported(platform)) {
      throw new Error(`Unsupported platform: ${platform}`);
    }
    this.logger.info(`Starting bookmark fetch for platform: ${platform}`);
    try {
      // Use platform-specific API implementation
      let api;
      switch (platform) {
        case 'twitter':
          api = new TwitterAPI();
          break;
        default:
          throw new Error(
            `API implementation not found for platform: ${platform}`
          );
      }
      const bookmarks = await api.fetchAllBookmarks(options, progressCallback);
      this.logger.info(
        `Successfully fetched ${bookmarks.length} bookmarks from ${platform}`
      );
      return bookmarks;
    } catch (error) {
      this.logger.error(`Failed to fetch bookmarks from ${platform}:`, error);
      throw error;
    }
  }
  // New method to get credentials from webRequest
  getWebRequestCredentials() {
    // This will be called from service worker context where globalCredentials is available
    if (typeof globalCredentials !== 'undefined') {
      return globalCredentials;
    }
    return null;
  }
  async fetchBookmarkPage(platform, cursor, credentials) {
    if (!this.isPlatformSupported(platform)) {
      throw new Error(`Unsupported platform: ${platform}`);
    }
    // For now, return empty result - will be implemented with actual API
    return {
      bookmarks: [],
      nextCursor: null,
      hasMore: false,
    };
  }
  detectPlatformFromUrl(url) {
    for (const [key, config] of this.platforms.entries()) {
      if (url.includes(config.domain)) {
        return key;
      }
      for (const altDomain of config.alternativeDomains) {
        if (url.includes(altDomain)) {
          return key;
        }
      }
    }
    return null;
  }
}
/**
 * Base API class for platform implementations
 */
class BaseAPI {
  constructor() {
    this.logger = new Logger(this.constructor.name);
  }
  async fetchAllBookmarks(options, progressCallback) {
    throw new Error('fetchAllBookmarks must be implemented by subclass');
  }
  async fetchBookmarkPage(cursor, credentials) {
    throw new Error('fetchBookmarkPage must be implemented by subclass');
  }
  sleep(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
/**
 * Twitter API Implementation
 */
class TwitterAPI extends BaseAPI {
  constructor() {
    super();
    this.baseURL = 'https://x.com/i/api/graphql';
    this.endpoints = {
      bookmarks: 'Bookmarks',
    };
    this.rateLimiter = new TwitterRateLimiter();
    this.credentialExtractor = new TwitterCredentialExtractor();
  }
  async fetchAllBookmarks(options, progressCallback) {
    const bookmarks = [];
    let cursor = null;
    let totalFetched = 0;
    let hasMore = true;
    let retryCount = 0;
    const maxRetries = options.maxRetries || 3;

    // Safety measures to prevent infinite loops
    let requestCount = 0;
    const maxRequests = 100; // Hard limit on number of requests
    let consecutiveEmptyResponses = 0;
    const maxEmptyResponses = 3; // Stop after 3 consecutive empty responses
    this.logger.info('Starting Twitter bookmark fetch');
    // Get credentials from webRequest (like the working old extension)
    let credentials = options.credentials;
    if (!credentials) {
      // Access globalCredentials from service worker context
      if (
        typeof globalCredentials !== 'undefined' &&
        globalCredentials.authorization &&
        globalCredentials['x-csrf-token']
      ) {
        credentials = globalCredentials;
        this.logger.info(
          'Successfully obtained Twitter credentials from webRequest'
        );
      } else {
        this.logger.error('No credentials available from webRequest');
        // Try to trigger credential extraction by making a test request
        this.logger.info('Attempting to trigger credential extraction...');
        try {
          // Try to extract credentials using content script injection
          const tabs = await chrome.tabs.query({
            active: true,
            currentWindow: true,
            url: ['*://twitter.com/*', '*://x.com/*'],
          });
          if (tabs.length > 0) {
            const tab = tabs[0];
            // Try to extract credentials directly from the page
            const results = await chrome.scripting.executeScript({
              target: { tabId: tab.id },
              func: () => {
                // Direct credential extraction function
                function extractTwitterCredentials() {
                  let authorization = null;
                  let csrfToken = null;
                  // Method 1: Extract from cookies
                  const cookies = document.cookie.split(';');
                  for (const cookie of cookies) {
                    const [name, value] = cookie.trim().split('=');
                    if (name === 'ct0') {
                      csrfToken = decodeURIComponent(value);
                      break;
                    }
                  }
                  // Method 2: Look for authorization in localStorage
                  try {
                    const localStorageKeys = Object.keys(localStorage);
                    for (const key of localStorageKeys) {
                      const value = localStorage.getItem(key);
                      if (value && value.includes('Bearer ')) {
                        const match = value.match(/Bearer [A-Za-z0-9%_-]+/);
                        if (match) {
                          authorization = match[0];
                          break;
                        }
                      }
                    }
                  } catch (e) {
                    // localStorage access might be restricted
                  }
                  // Method 3: Extract from page scripts
                  if (!authorization) {
                    const scripts = document.querySelectorAll('script');
                    for (const script of scripts) {
                      const content = script.textContent || script.innerHTML;
                      const bearerMatch = content.match(
                        /Bearer [A-Za-z0-9%_-]+/
                      );
                      if (bearerMatch) {
                        authorization = bearerMatch[0];
                        break;
                      }
                    }
                  }
                  return { authorization, csrfToken };
                }
                return extractTwitterCredentials();
              },
            });
            if (results && results[0] && results[0].result) {
              const extracted = results[0].result;
              if (extracted.authorization && extracted.csrfToken) {
                credentials = {
                  authorization: extracted.authorization,
                  'x-csrf-token': extracted.csrfToken,
                };
                this.logger.info(
                  'Successfully extracted credentials using direct page injection'
                );
              }
            }
          }
          if (
            !credentials ||
            !credentials.authorization ||
            !credentials['x-csrf-token']
          ) {
            this.logger.debug('Direct credential extraction failed');
            throw new Error(
              'Unable to extract Twitter credentials. Please:\n1. Make sure you are logged in to Twitter/X\n2. Navigate to your bookmarks page\n3. Wait a moment for the page to load\n4. Try the export again'
            );
          }
        } catch (fallbackError) {
          this.logger.debug(
            'Fallback credential extraction failed:',
            fallbackError
          );
          throw new Error(
            'Unable to extract Twitter credentials. Please:\n1. Make sure you are logged in to Twitter/X\n2. Navigate to your bookmarks page\n3. Wait a moment for the page to load\n4. Try the export again'
          );
        }
      }
    }
    while (hasMore && retryCount < maxRetries) {
      try {
        // Safety check: prevent infinite loops
        requestCount++;
        if (requestCount > maxRequests) {
          this.logger.warn(`Reached maximum request limit: ${maxRequests}`);
          hasMore = false;
          break;
        }

        this.logger.info(`Making request ${requestCount}/${maxRequests}`);

        // Check rate limits before making request
        await this.rateLimiter.checkRateLimit();
        const response = await this.fetchBookmarkPage(cursor, credentials);
        // Reset retry count on successful request
        retryCount = 0;
        if (!response.data?.bookmark_timeline_v2?.timeline?.instructions) {
          this.logger.warn('Invalid API response structure');
          break;
        }
        const entries =
          response.data.bookmark_timeline_v2.timeline.instructions[0]
            ?.entries || [];

        this.logger.info(
          `📊 Response Analysis - Total entries found: ${entries.length}`
        );
        this.logger.info(
          `🔍 Entry IDs: ${entries.map((e) => e.entryId).join(', ')}`
        );

        const bookmarkEntries = entries.filter(
          (entry) =>
            entry.entryId.startsWith('tweet-') ||
            entry.entryId.startsWith('promoted-tweet-')
        );

        this.logger.info(
          `✅ Filtered bookmark entries: ${bookmarkEntries.length}`
        );

        // Track consecutive empty responses
        if (bookmarkEntries.length === 0) {
          consecutiveEmptyResponses++;
          this.logger.warn(
            `Empty response ${consecutiveEmptyResponses}/${maxEmptyResponses}`
          );

          if (consecutiveEmptyResponses >= maxEmptyResponses) {
            this.logger.info('Stopping due to consecutive empty responses');
            hasMore = false;
            break;
          }
        } else {
          consecutiveEmptyResponses = 0; // Reset counter on successful fetch
        }

        bookmarks.push(...bookmarkEntries);
        totalFetched += bookmarkEntries.length;
        this.logger.info(
          `📈 Progress: Fetched ${bookmarkEntries.length} bookmarks, total: ${totalFetched}`
        );
        // Find next cursor
        const cursorEntry = entries.find((entry) =>
          entry.entryId.startsWith('cursor-bottom-')
        );

        // Check for multiple stop conditions
        if (cursorEntry) {
          cursor = cursorEntry.content.value;
          this.logger.info(
            `🔄 Next cursor found: ${cursor ? cursor.substring(0, 20) + '...' : 'null'}`
          );
          this.logger.info(
            `🛑 stopOnEmptyResponse: ${cursorEntry.content.stopOnEmptyResponse || false}`
          );

          // Check if cursor indicates to stop on empty response
          if (
            cursorEntry.content.stopOnEmptyResponse &&
            bookmarkEntries.length === 0
          ) {
            this.logger.info(
              '🛑 Stopping due to stopOnEmptyResponse flag with no bookmarks'
            );
            hasMore = false;
          }

          // Check if we're getting very few entries (likely end of results)
          if (entries.length <= 2) {
            this.logger.info(
              `Stopping due to minimal entries: ${entries.length}`
            );
            hasMore = false;
          }

          // Check if cursor value is empty or null
          if (!cursor || cursor.trim() === '') {
            this.logger.info('Stopping due to empty cursor value');
            hasMore = false;
          }
        } else {
          this.logger.info('No cursor found, stopping pagination');
          hasMore = false;
        }
        // Check stopping conditions
        if (options.maxCount && totalFetched >= options.maxCount) {
          hasMore = false;
          this.logger.info(`Reached max count limit: ${options.maxCount}`);
        }
        if (options.maxDate && this.isOlderThanDate(entries, options.maxDate)) {
          hasMore = false;
          this.logger.info(`Reached max date limit: ${options.maxDate}`);
        }
        // Progress callback
        if (progressCallback) {
          progressCallback({
            fetched: totalFetched,
            total: options.maxCount || 'unknown',
            percentage: options.maxCount
              ? Math.min((totalFetched / options.maxCount) * 100, 100)
              : null,
            cursor: cursor,
            hasMore: hasMore,
          });
        }
        // Rate limiting with intelligent delay
        const waitTime = this.rateLimiter.getNextDelay();
        await this.sleep(waitTime);
      } catch (error) {
        this.logger.error('Error fetching Twitter bookmarks:', error);
        if (this.rateLimiter.isRateLimitError(error)) {
          const waitTime = this.rateLimiter.handleRateLimit(error);
          this.logger.warn(`Rate limit hit, waiting ${waitTime}ms`);
          await this.sleep(waitTime);
          continue;
        }
        if (this.isRetryableError(error)) {
          retryCount++;
          const backoffTime = Math.min(1000 * Math.pow(2, retryCount), 30000);
          this.logger.warn(
            `Retryable error, attempt ${retryCount}/${maxRetries}, waiting ${backoffTime}ms`
          );
          await this.sleep(backoffTime);
          continue;
        }
        throw error;
      }
    }
    if (retryCount >= maxRetries) {
      throw new Error(`Failed to fetch bookmarks after ${maxRetries} retries`);
    }
    this.logger.info(
      `Twitter bookmark fetch completed: ${totalFetched} bookmarks`
    );
    return this.processTwitterBookmarks(bookmarks);
  }
  async fetchBookmarkPage(cursor, credentials) {
    // Get the correct GraphQL query ID
    const queryId = await this.getBookmarkQueryId();
    this.logger.info('Using GraphQL query ID:', queryId);
    const variables = {
      count: 20,
      includePromotedContent: true,
    };
    if (cursor) {
      variables.cursor = cursor;
    }
    // Features parameter matching working curl command
    const features = {
      rweb_video_screen_enabled: false,
      payments_enabled: false,
      profile_label_improvements_pcf_label_in_post_enabled: true,
      rweb_tipjar_consumption_enabled: true,
      verified_phone_label_enabled: false,
      creator_subscriptions_tweet_preview_api_enabled: true,
      responsive_web_graphql_timeline_navigation_enabled: true,
      responsive_web_graphql_skip_user_profile_image_extensions_enabled: false,
      premium_content_api_read_enabled: false,
      communities_web_enable_tweet_community_results_fetch: true,
      c9s_tweet_anatomy_moderator_badge_enabled: true,
      responsive_web_grok_analyze_button_fetch_trends_enabled: false,
      responsive_web_grok_analyze_post_followups_enabled: true,
      responsive_web_jetfuel_frame: true,
      responsive_web_grok_share_attachment_enabled: true,
      articles_preview_enabled: true,
      responsive_web_edit_tweet_api_enabled: true,
      graphql_is_translatable_rweb_tweet_is_translatable_enabled: true,
      view_counts_everywhere_api_enabled: true,
      longform_notetweets_consumption_enabled: true,
      responsive_web_twitter_article_tweet_consumption_enabled: true,
      tweet_awards_web_tipping_enabled: false,
      responsive_web_grok_show_grok_translated_post: false,
      responsive_web_grok_analysis_button_from_backend: false,
      creator_subscriptions_quote_tweet_preview_enabled: false,
      freedom_of_speech_not_reach_fetch_enabled: true,
      standardized_nudges_misinfo: true,
      tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled: true,
      longform_notetweets_rich_text_read_enabled: true,
      longform_notetweets_inline_media_enabled: true,
      responsive_web_grok_image_annotation_enabled: true,
      responsive_web_grok_community_note_auto_translation_is_enabled: false,
      responsive_web_enhance_cards_enabled: false,
      // Add the missing required features from the error message
      responsive_web_media_download_video_enabled: true,
      rweb_video_timestamps_enabled: true,
      responsive_web_graphql_exclude_directive_enabled: true,
      graphql_timeline_v2_bookmark_timeline: true,
      tweetypie_unmention_optimization_enabled: true,
    };
    const url = `${this.baseURL}/${queryId}/${this.endpoints.bookmarks}?variables=${encodeURIComponent(JSON.stringify(variables))}&features=${encodeURIComponent(JSON.stringify(features))}`;
    this.logger.info('Making request to URL:', url);
    this.logger.info('Request variables:', variables);
    const headers = {
      accept: '*/*',
      'accept-language':
        'tr-TR,tr;q=0.9,en-US;q=0.8,en;q=0.7,zh-CN;q=0.6,zh;q=0.5,bg;q=0.4,de;q=0.3',
      authorization: credentials.authorization,
      'cache-control': 'no-cache',
      'content-type': 'application/json',
      dnt: '1',
      pragma: 'no-cache',
      priority: 'u=1, i',
      'x-csrf-token': credentials['x-csrf-token'] || credentials.csrfToken,
      'x-twitter-active-user': 'yes',
      'x-twitter-auth-type': 'OAuth2Session',
      'x-twitter-client-language': 'tr',
      'sec-ch-ua':
        '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"macOS"',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'same-origin',
      'user-agent':
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
    };
    this.logger.debug('Making Twitter API request:', {
      url: url.split('?')[0],
      cursor: !!cursor,
    });
    const response = await fetch(url, {
      method: 'GET',
      headers: headers,
      credentials: 'include',
      referrer: 'https://x.com/i/bookmarks',
      referrerPolicy: 'strict-origin-when-cross-origin',
    });
    if (!response.ok) {
      const errorText = await response.text();
      this.logger.error('Twitter API error response:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText,
      });
      if (response.status === 429) {
        throw new TwitterRateLimitError(
          'Rate limit exceeded',
          response.headers
        );
      } else if (response.status === 401) {
        throw new TwitterAuthError(
          'Authentication failed - credentials may be expired'
        );
      } else if (response.status >= 500) {
        throw new TwitterServerError(
          `Twitter server error: ${response.status}`
        );
      }
      throw new Error(
        `Twitter API error: ${response.status} ${response.statusText}`
      );
    }
    const data = await response.json();
    // Validate response structure
    if (!data.data) {
      throw new Error('Invalid Twitter API response: missing data field');
    }
    return data;
  }
  processTwitterBookmarks(entries) {
    return entries
      .map((entry) => {
        const tweet = entry.content?.itemContent?.tweet_results?.result;
        if (!tweet) return null;
        return {
          id: tweet.rest_id,
          platform: 'twitter',
          author: {
            username: tweet.core?.user_results?.result?.legacy?.screen_name,
            displayName: tweet.core?.user_results?.result?.legacy?.name,
            profileImage:
              tweet.core?.user_results?.result?.legacy?.profile_image_url_https,
          },
          content: {
            text: tweet.legacy?.full_text,
            createdAt: tweet.legacy?.created_at,
            lang: tweet.legacy?.lang,
          },
          media: this.extractMedia(tweet.legacy?.entities),
          metrics: {
            retweets: tweet.legacy?.retweet_count,
            likes: tweet.legacy?.favorite_count,
            replies: tweet.legacy?.reply_count,
            quotes: tweet.legacy?.quote_count,
          },
          urls: this.extractUrls(tweet.legacy?.entities),
          bookmarkedAt: new Date().toISOString(),
        };
      })
      .filter(Boolean);
  }
  extractMedia(entities) {
    if (!entities?.media) return [];
    return entities.media.map((media) => ({
      type: media.type,
      url: media.media_url_https,
      displayUrl: media.display_url,
    }));
  }
  extractUrls(entities) {
    if (!entities?.urls) return [];
    return entities.urls.map((url) => ({
      shortUrl: url.url,
      expandedUrl: url.expanded_url,
      displayUrl: url.display_url,
    }));
  }
  isOlderThanDate(entries, maxDate) {
    return entries.some((entry) => {
      const sortIndex = entry.sortIndex;
      if (!sortIndex) return false;
      const timestamp = Number(BigInt(sortIndex) >> BigInt(20));
      const entryDate = new Date(timestamp);
      return entryDate < new Date(maxDate);
    });
  }
  isRetryableError(error) {
    // Network errors, temporary server errors, etc.
    return (
      error.name === 'TypeError' || // Network error
      error.name === 'TwitterServerError' ||
      (error.message &&
        (error.message.includes('fetch') ||
          error.message.includes('network') ||
          error.message.includes('timeout') ||
          error.message.includes('502') ||
          error.message.includes('503') ||
          error.message.includes('504')))
    );
  }
  async getBookmarkQueryId() {
    try {
      this.logger.info('Attempting to get bookmark query ID...');
      this.logger.info(
        'bookmarkEndpointUrl available:',
        typeof bookmarkEndpointUrl !== 'undefined'
          ? bookmarkEndpointUrl
          : 'undefined'
      );
      // First try to extract from captured bookmark endpoint URL (like old extension)
      if (typeof bookmarkEndpointUrl !== 'undefined' && bookmarkEndpointUrl) {
        const urlParts = bookmarkEndpointUrl.split('/');
        const graphqlIndex = urlParts.findIndex((part) => part === 'graphql');
        if (graphqlIndex !== -1 && urlParts[graphqlIndex + 1]) {
          const queryId = urlParts[graphqlIndex + 1];
          this.logger.info(
            'Successfully extracted query ID from webRequest URL:',
            queryId
          );
          return queryId;
        }
      }
      // Try to get query ID from content script
      const tabs = await chrome.tabs.query({
        active: true,
        currentWindow: true,
        url: ['*://twitter.com/*', '*://x.com/*'],
      });
      if (tabs.length > 0) {
        const tab = tabs[0];
        try {
          const results = await chrome.scripting.executeScript({
            target: { tabId: tab.id },
            func: () => {
              // Try to get query ID from the global query extractor
              if (
                window.abstractBookmark &&
                window.abstractBookmark.queryExtractor
              ) {
                return window.abstractBookmark.queryExtractor.getQueryId(
                  'bookmark_timeline_v2'
                );
              }
              return null;
            },
          });
          if (results && results[0] && results[0].result) {
            this.logger.info(
              'Successfully extracted query ID from content script'
            );
            return results[0].result;
          }
        } catch (error) {
          this.logger.warn(
            'Failed to extract query ID from content script:',
            error
          );
        }
      }
      // Fallback to known query IDs (these may need periodic updates)
      const fallbackQueryIds = [
        'qToeLeMs43Q8cr7tRYXmaQ',
        'H8OOoI-5ZE4NxgRr8lfyWg',
        'INUj5b-1Hl9GJhXN-2gqbg',
        'E3opETHurmVJflFsUBVuUQ',
      ];
      // Try each fallback query ID
      for (const queryId of fallbackQueryIds) {
        this.logger.info(`Trying fallback query ID: ${queryId}`);
        return queryId; // Return the first one for now, could be enhanced to test each
      }
      throw new Error('Could not determine bookmark query ID');
    } catch (error) {
      this.logger.error('Failed to get bookmark query ID:', error);
      throw error;
    }
  }
}
/**
 * Placeholder implementations for future platforms
 */
class LinkedInAPI extends BaseAPI {
  async fetchAllBookmarks(options, progressCallback) {
    throw new Error(
      'LinkedIn API not yet implemented - will be added in Epic 3'
    );
  }
  async fetchBookmarkPage(cursor, credentials) {
    throw new Error(
      'LinkedIn API not yet implemented - will be added in Epic 3'
    );
  }
}
class InstagramAPI extends BaseAPI {
  async fetchAllBookmarks(options, progressCallback) {
    throw new Error(
      'Instagram API not yet implemented - will be added in Epic 3'
    );
  }
  async fetchBookmarkPage(cursor, credentials) {
    throw new Error(
      'Instagram API not yet implemented - will be added in Epic 3'
    );
  }
}
class RedditAPI extends BaseAPI {
  async fetchAllBookmarks(options, progressCallback) {
    throw new Error('Reddit API not yet implemented - will be added in Epic 3');
  }
  async fetchBookmarkPage(cursor, credentials) {
    throw new Error('Reddit API not yet implemented - will be added in Epic 3');
  }
}
/**
 * Twitter-specific error classes
 */
class TwitterRateLimitError extends Error {
  constructor(message, headers) {
    super(message);
    this.name = 'TwitterRateLimitError';
    this.headers = headers;
    this.resetTime = headers?.get('x-rate-limit-reset');
    this.remaining = headers?.get('x-rate-limit-remaining');
  }
}
class TwitterAuthError extends Error {
  constructor(message) {
    super(message);
    this.name = 'TwitterAuthError';
  }
}
class TwitterServerError extends Error {
  constructor(message) {
    super(message);
    this.name = 'TwitterServerError';
  }
}
/**
 * Twitter Rate Limiter
 */
class TwitterRateLimiter {
  constructor() {
    this.requestTimes = [];
    this.rateLimitWindow = 15 * 60 * 1000; // 15 minutes
    this.maxRequests = 75; // Twitter's rate limit for bookmark timeline
    this.baseDelay = 1000; // 1 second base delay
    this.lastRateLimitReset = null;
  }
  async checkRateLimit() {
    const now = Date.now();
    // Remove old requests outside the window
    this.requestTimes = this.requestTimes.filter(
      (time) => now - time < this.rateLimitWindow
    );
    // Check if we're approaching the limit
    if (this.requestTimes.length >= this.maxRequests - 5) {
      const oldestRequest = Math.min(...this.requestTimes);
      const waitTime = this.rateLimitWindow - (now - oldestRequest) + 1000;
      if (waitTime > 0) {
        throw new Error(`Rate limit prevention: waiting ${waitTime}ms`);
      }
    }
    // Record this request
    this.requestTimes.push(now);
  }
  getNextDelay() {
    const requestCount = this.requestTimes.length;
    const utilizationRatio = requestCount / this.maxRequests;
    // Increase delay as we approach rate limit
    const dynamicDelay = this.baseDelay * (1 + utilizationRatio * 2);
    // Add some jitter to avoid thundering herd
    const jitter = Math.random() * 500;
    return Math.floor(dynamicDelay + jitter);
  }
  isRateLimitError(error) {
    return (
      error instanceof TwitterRateLimitError ||
      error.message.includes('rate limit') ||
      error.message.includes('429')
    );
  }
  handleRateLimit(error) {
    if (error instanceof TwitterRateLimitError && error.resetTime) {
      const resetTime = parseInt(error.resetTime) * 1000;
      const waitTime = resetTime - Date.now() + 5000; // Add 5 second buffer
      return Math.max(waitTime, 60000); // Wait at least 1 minute
    }
    // Default exponential backoff
    return Math.min(
      15 * 60 * 1000,
      60000 * Math.pow(2, this.requestTimes.length % 4)
    );
  }
}
/**
 * Twitter Credential Extractor
 */
class TwitterCredentialExtractor {
  constructor() {
    this.logger = new Logger('TwitterCredentialExtractor');
  }
  async extractCredentials() {
    try {
      this.logger.debug('Extracting Twitter credentials via content script');
      // Get active Twitter tab
      const tabs = await chrome.tabs.query({
        active: true,
        currentWindow: true,
        url: ['*://twitter.com/*', '*://x.com/*'],
      });
      if (tabs.length === 0) {
        this.logger.debug(
          'No active Twitter tab found. Please open Twitter/X in the current tab.'
        );
        return null;
      }
      const tab = tabs[0];
      // Execute script to extract credentials
      const results = await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        func: this.extractCredentialsFromPage,
      });
      if (!results || results.length === 0 || !results[0].result) {
        this.logger.debug('Failed to extract credentials from Twitter page');
        return null;
      }
      const credentials = results[0].result;
      if (credentials) {
        this.validateCredentials(credentials);
        this.logger.debug(
          'Successfully extracted and validated Twitter credentials'
        );
        return credentials;
      } else {
        this.logger.debug('No credentials extracted from Twitter page');
        return null;
      }
    } catch (error) {
      this.logger.debug('Failed to extract Twitter credentials:', error);
      return null;
    }
  }
  // This function will be injected into the Twitter page
  extractCredentialsFromPage() {
    try {
      // Try to get credentials from the MAIN world content script
      if (
        window.abstractBookmark &&
        window.abstractBookmark.credentialExtractor
      ) {
        const credentials =
          window.abstractBookmark.credentialExtractor.getCurrentCredentials();
        if (credentials) {
          return credentials;
        }
      }
      // Fallback: try to extract directly
      let authorization = null;
      let csrfToken = null;
      // Method 1: Extract from cookies
      const cookies = document.cookie.split(';');
      for (const cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'ct0') {
          csrfToken = decodeURIComponent(value);
          break;
        }
      }
      // Method 2: Look for auth token in localStorage
      try {
        const localStorageKeys = Object.keys(localStorage);
        for (const key of localStorageKeys) {
          const value = localStorage.getItem(key);
          if (value && value.includes('Bearer ')) {
            const match = value.match(/Bearer [A-Za-z0-9%_-]+/);
            if (match) {
              authorization = match[0];
              break;
            }
          }
        }
      } catch (e) {
        // localStorage access might be restricted
      }
      // Method 3: Extract from page scripts if other methods fail
      if (!authorization) {
        const scripts = document.querySelectorAll('script');
        for (const script of scripts) {
          const content = script.textContent || script.innerHTML;
          const bearerMatch = content.match(/Bearer [A-Za-z0-9%_-]+/);
          if (bearerMatch) {
            authorization = bearerMatch[0];
            break;
          }
        }
      }
      if (!csrfToken) {
        // Try to find CSRF token in meta tags or scripts
        const csrfMeta = document.querySelector('meta[name="csrf-token"]');
        if (csrfMeta) {
          csrfToken = csrfMeta.getAttribute('content');
        } else {
          // Look in scripts
          const scripts = document.querySelectorAll('script');
          for (const script of scripts) {
            const content = script.textContent || script.innerHTML;
            const csrfMatch = content.match(/"csrf_token":"([^"]+)"/);
            if (csrfMatch) {
              csrfToken = csrfMatch[1];
              break;
            }
          }
        }
      }
      if (authorization && csrfToken) {
        return {
          authorization: authorization,
          csrfToken: csrfToken,
        };
      }
      // Silently fail - don't throw error to avoid console spam
      return null;
    } catch (error) {
      // Silently fail - don't throw error to avoid console spam
      return null;
    }
  }
  validateCredentials(credentials) {
    if (!credentials) {
      throw new Error('Credentials are required');
    }
    if (
      !credentials.authorization ||
      !credentials.authorization.startsWith('Bearer ')
    ) {
      throw new Error('Valid authorization token is required');
    }
    if (!credentials.csrfToken) {
      throw new Error('CSRF token is required');
    }
    return true;
  }
}
