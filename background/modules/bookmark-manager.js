/**
 * Bookmark Manager - Core bookmark management functionality
 */

import { Logger } from './logger.js';
import { StorageManager } from './storage-manager.js';

export class BookmarkManager {
  constructor() {
    this.logger = new Logger('BookmarkManager');
    this.storageManager = new StorageManager();
  }

  async saveBookmark(bookmark) {
    try {
      this.validateBookmark(bookmark);
      
      const bookmarkId = await this.storageManager.saveBookmark(bookmark);
      
      this.logger.info(`Bookmark saved: ${bookmarkId}`);
      return bookmarkId;
    } catch (error) {
      this.logger.error('Failed to save bookmark:', error);
      throw error;
    }
  }

  async getBookmark(bookmarkId) {
    try {
      const bookmark = await this.storageManager.getBookmark(bookmarkId);
      
      if (!bookmark) {
        this.logger.warn(`Bookmark not found: ${bookmarkId}`);
        return null;
      }
      
      return bookmark;
    } catch (error) {
      this.logger.error('Failed to get bookmark:', error);
      throw error;
    }
  }

  async deleteBookmark(bookmarkId) {
    try {
      await this.storageManager.deleteBookmark(bookmarkId);
      this.logger.info(`Bookmark deleted: ${bookmarkId}`);
    } catch (error) {
      this.logger.error('Failed to delete bookmark:', error);
      throw error;
    }
  }

  async searchBookmarks(query, filters = {}) {
    try {
      const index = await this.storageManager.getBookmarkIndex();
      const bookmarkIds = Object.keys(index);
      
      let filteredIds = bookmarkIds;
      
      // Apply platform filter
      if (filters.platform) {
        filteredIds = filteredIds.filter(id => 
          index[id].platform === filters.platform
        );
      }
      
      // Apply date range filter
      if (filters.startDate || filters.endDate) {
        filteredIds = filteredIds.filter(id => {
          const createdAt = new Date(index[id].createdAt);
          const startDate = filters.startDate ? new Date(filters.startDate) : new Date(0);
          const endDate = filters.endDate ? new Date(filters.endDate) : new Date();
          
          return createdAt >= startDate && createdAt <= endDate;
        });
      }
      
      // Apply author filter
      if (filters.author) {
        filteredIds = filteredIds.filter(id => 
          index[id].author?.toLowerCase().includes(filters.author.toLowerCase())
        );
      }
      
      // Load full bookmark data for text search
      if (query) {
        const bookmarks = await Promise.all(
          filteredIds.map(id => this.storageManager.getBookmark(id))
        );
        
        const searchResults = bookmarks.filter(bookmark => 
          bookmark && this.matchesQuery(bookmark, query)
        );
        
        return searchResults;
      }
      
      // Load all filtered bookmarks
      const bookmarks = await Promise.all(
        filteredIds.map(id => this.storageManager.getBookmark(id))
      );
      
      return bookmarks.filter(Boolean);
    } catch (error) {
      this.logger.error('Failed to search bookmarks:', error);
      throw error;
    }
  }

  async getStatistics() {
    try {
      const index = await this.storageManager.getBookmarkIndex();
      const bookmarkIds = Object.keys(index);
      
      const stats = {
        total: bookmarkIds.length,
        byPlatform: {},
        byMonth: {},
        storageUsage: await this.storageManager.getStorageUsage()
      };
      
      // Calculate platform statistics
      bookmarkIds.forEach(id => {
        const bookmark = index[id];
        const platform = bookmark.platform || 'unknown';
        
        stats.byPlatform[platform] = (stats.byPlatform[platform] || 0) + 1;
      });
      
      // Calculate monthly statistics
      bookmarkIds.forEach(id => {
        const bookmark = index[id];
        const date = new Date(bookmark.savedAt);
        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        
        stats.byMonth[monthKey] = (stats.byMonth[monthKey] || 0) + 1;
      });
      
      return stats;
    } catch (error) {
      this.logger.error('Failed to get statistics:', error);
      throw error;
    }
  }

  async createBackup() {
    try {
      this.logger.info('Creating bookmark backup');
      
      const index = await this.storageManager.getBookmarkIndex();
      const bookmarkIds = Object.keys(index);
      
      const bookmarks = await Promise.all(
        bookmarkIds.map(id => this.storageManager.getBookmark(id))
      );
      
      const backup = {
        version: '1.0.0',
        createdAt: new Date().toISOString(),
        bookmarks: bookmarks.filter(Boolean),
        statistics: await this.getStatistics()
      };
      
      const backupId = `backup_${Date.now()}`;
      await this.storageManager.saveExportData(backupId, backup);
      
      this.logger.info(`Backup created: ${backupId}`);
      return backupId;
    } catch (error) {
      this.logger.error('Failed to create backup:', error);
      throw error;
    }
  }

  async restoreFromBackup(backupId) {
    try {
      this.logger.info(`Restoring from backup: ${backupId}`);
      
      const backup = await this.storageManager.getExportData(backupId);
      
      if (!backup || !backup.bookmarks) {
        throw new Error('Invalid backup data');
      }
      
      let restoredCount = 0;
      
      for (const bookmark of backup.bookmarks) {
        try {
          await this.saveBookmark(bookmark);
          restoredCount++;
        } catch (error) {
          this.logger.warn(`Failed to restore bookmark ${bookmark.id}:`, error);
        }
      }
      
      this.logger.info(`Restored ${restoredCount} bookmarks from backup`);
      return restoredCount;
    } catch (error) {
      this.logger.error('Failed to restore from backup:', error);
      throw error;
    }
  }

  validateBookmark(bookmark) {
    if (!bookmark) {
      throw new Error('Bookmark data is required');
    }
    
    if (!bookmark.platform) {
      throw new Error('Bookmark platform is required');
    }
    
    if (!bookmark.content || !bookmark.content.text) {
      throw new Error('Bookmark content is required');
    }
    
    if (!bookmark.author || !bookmark.author.username) {
      throw new Error('Bookmark author information is required');
    }
  }

  matchesQuery(bookmark, query) {
    const searchText = query.toLowerCase();
    
    // Search in content text
    if (bookmark.content?.text?.toLowerCase().includes(searchText)) {
      return true;
    }
    
    // Search in author name
    if (bookmark.author?.displayName?.toLowerCase().includes(searchText)) {
      return true;
    }
    
    // Search in author username
    if (bookmark.author?.username?.toLowerCase().includes(searchText)) {
      return true;
    }
    
    // Search in URLs
    if (bookmark.urls?.some(url => 
      url.expandedUrl?.toLowerCase().includes(searchText) ||
      url.displayUrl?.toLowerCase().includes(searchText)
    )) {
      return true;
    }
    
    return false;
  }

  async cleanupOldBookmarks(maxAge = 365) {
    try {
      this.logger.info(`Cleaning up bookmarks older than ${maxAge} days`);
      
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - maxAge);
      
      const index = await this.storageManager.getBookmarkIndex();
      const bookmarkIds = Object.keys(index);
      
      let deletedCount = 0;
      
      for (const bookmarkId of bookmarkIds) {
        const bookmark = index[bookmarkId];
        const savedAt = new Date(bookmark.savedAt);
        
        if (savedAt < cutoffDate) {
          await this.deleteBookmark(bookmarkId);
          deletedCount++;
        }
      }
      
      this.logger.info(`Cleaned up ${deletedCount} old bookmarks`);
      return deletedCount;
    } catch (error) {
      this.logger.error('Failed to cleanup old bookmarks:', error);
      throw error;
    }
  }

  async duplicateDetection() {
    try {
      this.logger.info('Running duplicate detection');
      
      const index = await this.storageManager.getBookmarkIndex();
      const bookmarkIds = Object.keys(index);
      
      const bookmarks = await Promise.all(
        bookmarkIds.map(id => this.storageManager.getBookmark(id))
      );
      
      const duplicates = [];
      const seen = new Map();
      
      for (const bookmark of bookmarks.filter(Boolean)) {
        const key = `${bookmark.platform}_${bookmark.id}`;
        
        if (seen.has(key)) {
          duplicates.push({
            original: seen.get(key),
            duplicate: bookmark
          });
        } else {
          seen.set(key, bookmark);
        }
      }
      
      this.logger.info(`Found ${duplicates.length} duplicate bookmarks`);
      return duplicates;
    } catch (error) {
      this.logger.error('Failed to detect duplicates:', error);
      throw error;
    }
  }

  async removeDuplicates() {
    try {
      const duplicates = await this.duplicateDetection();
      
      let removedCount = 0;
      
      for (const { duplicate } of duplicates) {
        const bookmarkId = this.storageManager.generateBookmarkId(duplicate);
        await this.deleteBookmark(bookmarkId);
        removedCount++;
      }
      
      this.logger.info(`Removed ${removedCount} duplicate bookmarks`);
      return removedCount;
    } catch (error) {
      this.logger.error('Failed to remove duplicates:', error);
      throw error;
    }
  }
}
