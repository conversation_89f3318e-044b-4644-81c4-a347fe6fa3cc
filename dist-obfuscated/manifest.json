{"manifest_version": 3, "name": "__MSG_appName__", "version": "1.0.0", "description": "__MSG_appDescription__", "author": "Aphedra Team", "default_locale": "en", "permissions": ["storage", "scripting", "downloads", "webRequest"], "host_permissions": ["https://twitter.com/*", "https://x.com/*"], "background": {"service_worker": "background/service-worker.js", "type": "module"}, "content_scripts": [{"matches": ["https://twitter.com/*", "https://x.com/*"], "js": ["content/platforms/twitter-injector.js"], "run_at": "document_start"}, {"matches": ["https://twitter.com/*", "https://x.com/*"], "js": ["content/platforms/twitter-api.js"], "run_at": "document_start", "world": "MAIN"}], "action": {"default_popup": "popup/popup.html", "default_title": "__MSG_appName__", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "options_page": "options/options.html", "icons": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "web_accessible_resources": [{"resources": ["assets/*", "ui/*", "shared/*"], "matches": ["https://twitter.com/*", "https://x.com/*"]}], "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'; style-src 'self' 'unsafe-inline';"}}