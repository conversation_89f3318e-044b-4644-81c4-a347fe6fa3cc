/**
 * Content Script i18n Utility
 * Provides localization support for content scripts where Chrome i18n API is limited
 */

class ContentI18n {
  constructor() {
    this.messages = {};
    this.currentLanguage = 'en';
    this.fallbackLanguage = 'en';
    this.initialized = false;
  }

  /**
   * Initialize the content i18n system
   * @returns {Promise<void>}
   */
  async init() {
    try {
      // Get current language from storage
      const { settings } = await chrome.storage.sync.get('settings');
      this.currentLanguage = settings?.language || 'en';

      // Load messages for current language
      await this.loadMessages(this.currentLanguage);

      // Load fallback messages if different from current
      if (this.currentLanguage !== this.fallbackLanguage) {
        await this.loadMessages(this.fallbackLanguage, true);
      }

      this.initialized = true;
      console.log(
        `Content i18n initialized for language: ${this.currentLanguage}`
      );
    } catch (error) {
      console.error('Failed to initialize content i18n:', error);
      // Fallback to English
      this.currentLanguage = 'en';
      await this.loadMessages('en');
      this.initialized = true;
    }
  }

  /**
   * Load messages for a specific language
   * @param {string} language - Language code
   * @param {boolean} isFallback - Whether this is fallback language
   * @returns {Promise<void>}
   */
  async loadMessages(language, isFallback = false) {
    try {
      // Since we can't directly access _locales from content script,
      // we'll use a predefined message set or request from background
      const response = await chrome.runtime.sendMessage({
        action: 'get_i18n_messages',
        language: language,
      });

      if (response && response.success && response.messages) {
        if (isFallback) {
          this.fallbackMessages = response.messages;
        } else {
          this.messages = response.messages;
        }
      } else {
        // Use hardcoded messages as last resort
        this.messages = this.getHardcodedMessages(language);
      }
    } catch (error) {
      console.error(`Failed to load messages for ${language}:`, error);
      if (!isFallback) {
        this.messages = this.getHardcodedMessages(language);
      }
    }
  }

  /**
   * Get a localized message
   * @param {string} key - Message key
   * @param {string|string[]} substitutions - Optional substitutions
   * @returns {string} Localized message
   */
  getMessage(key, substitutions = null) {
    if (!this.initialized) {
      console.warn('Content i18n not initialized, using key as fallback');
      return key;
    }

    let message = this.messages[key]?.message;

    // Try fallback if message not found
    if (!message && this.fallbackMessages) {
      message = this.fallbackMessages[key]?.message;
    }

    // Use key as last resort
    if (!message) {
      console.warn(`Missing translation for key: ${key}`);
      return key;
    }

    // Handle substitutions
    if (substitutions) {
      if (Array.isArray(substitutions)) {
        substitutions.forEach((sub, index) => {
          message = message.replace(`$${index + 1}`, sub);
        });
      } else {
        message = message.replace('$1', substitutions);
      }
    }

    return message;
  }

  /**
   * Update element text with localized message
   * @param {HTMLElement} element - Element to update
   * @param {string} key - Message key
   * @param {string|string[]} substitutions - Optional substitutions
   */
  updateElement(element, key, substitutions = null) {
    if (!element) return;

    const message = this.getMessage(key, substitutions);
    element.textContent = message;
  }

  /**
   * Update element HTML with localized message
   * @param {HTMLElement} element - Element to update
   * @param {string} key - Message key
   * @param {string|string[]} substitutions - Optional substitutions
   */
  updateElementHTML(element, key, substitutions = null) {
    if (!element) return;

    const message = this.getMessage(key, substitutions);
    element.innerHTML = message;
  }

  /**
   * Get hardcoded messages as fallback
   * @param {string} language - Language code
   * @returns {Object} Messages object
   */
  getHardcodedMessages(language) {
    const messages = {
      en: {
        // Content script specific messages
        content_exportButton: { message: 'Export Bookmarks' },
        content_exportProgress: { message: 'Exporting...' },
        content_exportComplete: { message: 'Export Complete!' },
        content_exportError: { message: 'Export Failed' },
        content_loadingBookmarks: { message: 'Loading bookmarks...' },
        content_bookmarksFound: { message: '$1 bookmarks found' },
        content_noBookmarks: { message: 'No bookmarks found' },
        content_clickToExport: { message: 'Click to export your bookmarks' },
        content_exportInProgress: { message: 'Export in progress...' },
        content_pleaseWait: { message: 'Please wait...' },
        content_cancel: { message: 'Cancel' },
        content_retry: { message: 'Retry' },
        content_close: { message: 'Close' },

        // Error messages
        content_error_networkError: { message: 'Network error occurred' },
        content_error_authError: { message: 'Authentication error' },
        content_error_rateLimit: { message: 'Rate limit exceeded' },
        content_error_unknown: { message: 'Unknown error occurred' },

        // Status messages
        content_status_initializing: { message: 'Initializing...' },
        content_status_connecting: { message: 'Connecting...' },
        content_status_processing: { message: 'Processing bookmarks...' },
        content_status_downloading: { message: 'Downloading data...' },
        content_status_complete: { message: 'Export completed successfully' },
      },

      tr: {
        // Turkish translations
        content_exportButton: { message: 'Yer İmlerini Dışa Aktar' },
        content_exportProgress: { message: 'Dışa aktarılıyor...' },
        content_exportComplete: { message: 'Dışa Aktarma Tamamlandı!' },
        content_exportError: { message: 'Dışa Aktarma Başarısız' },
        content_loadingBookmarks: { message: 'Yer imleri yükleniyor...' },
        content_bookmarksFound: { message: '$1 yer imi bulundu' },
        content_noBookmarks: { message: 'Yer imi bulunamadı' },
        content_clickToExport: {
          message: 'Yer imlerinizi dışa aktarmak için tıklayın',
        },
        content_exportInProgress: { message: 'Dışa aktarma devam ediyor...' },
        content_pleaseWait: { message: 'Lütfen bekleyin...' },
        content_cancel: { message: 'İptal' },
        content_retry: { message: 'Tekrar Dene' },
        content_close: { message: 'Kapat' },

        // Error messages
        content_error_networkError: { message: 'Ağ hatası oluştu' },
        content_error_authError: { message: 'Kimlik doğrulama hatası' },
        content_error_rateLimit: { message: 'Hız sınırı aşıldı' },
        content_error_unknown: { message: 'Bilinmeyen hata oluştu' },

        // Status messages
        content_status_initializing: { message: 'Başlatılıyor...' },
        content_status_connecting: { message: 'Bağlanıyor...' },
        content_status_processing: { message: 'Yer imleri işleniyor...' },
        content_status_downloading: { message: 'Veri indiriliyor...' },
        content_status_complete: {
          message: 'Dışa aktarma başarıyla tamamlandı',
        },
      },
    };

    return messages[language] || messages.en;
  }

  /**
   * Listen for language changes
   */
  setupLanguageChangeListener() {
    chrome.storage.onChanged.addListener((changes, namespace) => {
      if (
        namespace === 'sync' &&
        changes.settings &&
        changes.settings.newValue?.language
      ) {
        const newLanguage = changes.settings.newValue.language;
        if (newLanguage !== this.currentLanguage) {
          this.currentLanguage = newLanguage;
          this.loadMessages(newLanguage).then(() => {
            // Trigger re-translation of existing elements
            this.retranslateElements();
          });
        }
      }
    });
  }

  /**
   * Re-translate all elements with data-i18n attribute
   */
  retranslateElements() {
    const elements = document.querySelectorAll('[data-content-i18n]');
    elements.forEach((element) => {
      const key = element.getAttribute('data-content-i18n');
      if (key) {
        this.updateElement(element, key);
      }
    });
  }

  /**
   * Auto-translate elements with data-content-i18n attribute
   */
  autoTranslateElements() {
    const elements = document.querySelectorAll('[data-content-i18n]');
    elements.forEach((element) => {
      const key = element.getAttribute('data-content-i18n');
      if (key) {
        this.updateElement(element, key);
      }
    });
  }
}

// Make ContentI18n class available globally
window.ContentI18n = ContentI18n;

// Create global instance
window.contentI18n = new ContentI18n();

console.log('Content i18n utility loaded and global instance created');
