# AbstractBookmark Icons

This directory contains the extension icons in various sizes.

## Required Icons

- `icon16.png` - 16x16 pixels (toolbar icon)
- `icon32.png` - 32x32 pixels (extension management page)
- `icon48.png` - 48x48 pixels (extension management page)
- `icon128.png` - 128x128 pixels (Chrome Web Store)

## Design Guidelines

The AbstractBookmark icon should:
- Use a modern, clean design
- Be recognizable at small sizes
- Use colors that work well on both light and dark backgrounds
- Represent bookmarking/saving functionality

## Current Status

Icons need to be created. For development, you can use placeholder icons or create simple colored squares with the "AB" text.

## Creating Icons

You can create icons using:
- Design tools like Figma, Sketch, or Adobe Illustrator
- Online icon generators
- Simple image editing tools

The icon should be saved as PNG files with transparent backgrounds.
