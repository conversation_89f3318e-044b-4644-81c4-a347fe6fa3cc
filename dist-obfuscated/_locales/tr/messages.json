{"appName": {"message": "AbstractBookmark", "description": "The name of the extension"}, "appDescription": {"message": "Sosyal medya <PERSON>ı için gelişmiş yer imi yönetimi ve dışa aktarma aracı", "description": "The description of the extension"}, "popup_detectingPlatform": {"message": "Platform algılanıyor...", "description": "Status message when detecting the current platform"}, "popup_navigateToBookmarks": {"message": "Kaydettiğiniz tweet'leri dışa aktarmak için Twitter/X yer imleri sayfanıza gidin.", "description": "Message shown when user is not on bookmarks page"}, "popup_openTwitterBookmarks": {"message": "Twitter Yer İmlerini <PERSON> →", "description": "Link text to open Twitter bookmarks page"}, "popup_bookmarksDetected": {"message": "yer imi algı<PERSON>ı", "description": "Label for bookmark count"}, "popup_downloadAllBookmarks": {"message": "<PERSON><PERSON><PERSON>", "description": "Main export button text"}, "popup_exportAsJson": {"message": "Tam metadata ile JSON olarak dışa aktar", "description": "Subtitle for JSON export option"}, "popup_preparingExport": {"message": "Dışa aktarma hazırlanıyor...", "description": "Initial progress message"}, "popup_cancel": {"message": "İptal", "description": "Cancel button text"}, "popup_exportOptions": {"message": "Dışa Aktarma Seçenekleri", "description": "Export options section title"}, "popup_exportFormat": {"message": "Dışa Aktarma Formatı:", "description": "Export format label"}, "popup_formatJson": {"message": "JSON - Tam metadata", "description": "JSON format option"}, "popup_formatExcel": {"message": "Excel - Elektronik tablo formatı", "description": "Excel format option"}, "popup_formatCsv": {"message": "CSV - Virgülle ayrılmış", "description": "CSV format option"}, "popup_formatHtml": {"message": "HTML - Web sayfası", "description": "HTML format option"}, "popup_includeMedia": {"message": "Medya URL'leri ve metadata'yı dahil et", "description": "Include media option"}, "popup_includeMetrics": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> met<PERSON> da<PERSON> et", "description": "Include metrics option"}, "popup_expandUrls": {"message": "Kısaltılmış URL'leri genişlet", "description": "Expand URLs option"}, "popup_exportCompleted": {"message": "Dışa aktarma başarıyla tamamlandı!", "description": "Success message when export is completed"}, "popup_settings": {"message": "<PERSON><PERSON><PERSON>", "description": "Settings button text"}, "popup_help": {"message": "Yardım", "description": "Help button text"}, "status_onTwitterGoToBookmarks": {"message": "Twitter'da - Yer imlerine git", "description": "Status when on Twitter but not on bookmarks page"}, "status_unsupportedPlatform": {"message": "Desteklenmeyen platform", "description": "Status when on unsupported platform"}, "status_twitterBookmarksDetected": {"message": "Twitter yer imleri algılandı", "description": "Status when on Twitter bookmarks page"}, "status_credentialsReady": {"message": "(Kimlik bilgileri hazır)", "description": "Status when credentials are available"}, "status_loading": {"message": "(Yükleniyor...)", "description": "Status when loading"}, "error_unableToDetectPage": {"message": "Mevcut sayfa algılanamıyor", "description": "Error when unable to detect current page"}, "error_twitterNotDetected": {"message": "Twitter/X algılanmadı", "description": "Error when Twitter is not detected"}, "error_detectingPlatform": {"message": "Platform algılanırken hata", "description": "Error when platform detection fails"}, "export_formatJson": {"message": "Tam metadata ile JSON olarak dışa aktar", "description": "JSON export format description"}, "export_formatExcel": {"message": "Excel elektronik tablosu olarak dışa aktar", "description": "Excel export format description"}, "export_formatCsv": {"message": "CSV dosyası olarak dışa aktar", "description": "CSV export format description"}, "export_formatHtml": {"message": "HTML web sayfası olarak dışa aktar", "description": "HTML export format description"}, "progress_initializingExport": {"message": "Dışa aktarma başlatılıyor...", "description": "Progress message when initializing export"}, "progress_checkingCredentials": {"message": "Kimlik bilgileri kontrol ediliyor...", "description": "Progress message when checking credentials"}, "progress_configuringOptions": {"message": "Dışa aktarma seçenekleri yapılandırılıyor...", "description": "Progress message when configuring options"}, "progress_startingExport": {"message": "Dışa aktarma başlatılıyor...", "description": "Progress message when starting export"}, "progress_exportStarted": {"message": "Dışa aktarma başarıyla başlatıldı!", "description": "Progress message when export started"}, "error_credentialsNotReady": {"message": "Twitter kimlik bilgileri hazır değil. Lütfen:\n1. Twitter/X'e giriş yaptığınızdan emin olun\n2. Bu sayfayı yenileyin ve tamamen yüklenmesini bekleyin\n3. Dışa aktarmayı tekrar deneyin", "description": "Error message when credentials are not ready"}, "error_failedToStartExport": {"message": "Dışa aktarma başlatılamadı", "description": "Error message when export fails to start"}, "error_exportFailed": {"message": "Dışa aktarma başarısız: ", "description": "Error message prefix when export fails"}, "error_unknownError": {"message": "Bilinmeyen hata", "description": "Error message for unknown errors"}, "error_exportCancelled": {"message": "Dışa aktarma kullanıcı tarafından iptal edildi", "description": "Message when export is cancelled by user"}, "success_exportCompleted": {"message": "Dışa aktarma tamamlandı! $COUNT$ yer imi dışa aktarıldı.", "description": "Success message when export is completed", "placeholders": {"count": {"content": "$1", "example": "150"}}}, "button_tryExtractCredentials": {"message": "Kimlik Bilgilerini Çıkarmayı Dene", "description": "Button text to try extracting credentials"}, "button_extracting": {"message": "Çıkarılıyor...", "description": "Button text when extracting credentials"}, "button_credentialsExtracted": {"message": "✅ Kimlik Bilgileri Çıkarıldı!", "description": "Button text when credentials are extracted successfully"}, "button_extractionFailed": {"message": "❌ Çıkarma Başarısız", "description": "Button text when credential extraction fails"}, "button_error": {"message": "❌ Hata", "description": "Button text when an error occurs"}, "help_credentialsNotReady": {"message": "<strong><PERSON>lik bilgileri hazır değ<PERSON>:</strong><br>1. Bu Twitter sayfasını yenileyin<br>2. <PERSON><PERSON><PERSON><PERSON> tamamen yü<PERSON><PERSON><PERSON><PERSON> be<PERSON><br>3. <PERSON><PERSON><PERSON><PERSON> aktarmayı tekrar deneyin", "description": "Help message when credentials are not ready"}, "options_title": {"message": "AbstractBookmark Ayarları", "description": "Options page title"}, "options_exportOptionsGroup": {"message": "Dışa Aktarma Seçenekleri", "description": "Export options group title"}, "options_performanceGroup": {"message": "Performans", "description": "Performance options group title"}, "options_privacyGroup": {"message": "Gizlilik", "description": "Privacy options group title"}, "options_languageGroup": {"message": "Dil", "description": "Language options group title"}, "options_includeMedia": {"message": "Medya URL'leri ve metadata'yı dahil et", "description": "Include media option in settings"}, "options_includeMetrics": {"message": "<PERSON>t<PERSON><PERSON><PERSON>im metriklerini dahil et (beğeni, retweet, vb.)", "description": "Include metrics option in settings"}, "options_expandUrls": {"message": "Kısaltılmış URL'leri genişlet", "description": "Expand URLs option in settings"}, "options_enableRateLimit": {"message": "<PERSON><PERSON>z sınırlama korumasını etkinleştir", "description": "Rate limiting option in settings"}, "options_enableRetry": {"message": "Hatalarda otomatik yeniden denemeyi etkinleştir", "description": "Retry option in settings"}, "options_clearCredentials": {"message": "Dışa aktarma sonrası kimlik bilgilerini temizle", "description": "Clear credentials option in settings"}, "options_anonymizeData": {"message": "Dışa aktarılan verileri anonimleştir", "description": "Anonymize data option in settings"}, "options_autoDetectLanguage": {"message": "Tarayıcı ayarlarından otomatik dil algıla", "description": "Auto-detect language option in settings"}, "options_selectLanguage": {"message": "<PERSON><PERSON>:", "description": "Language selection label"}, "options_languageInfo": {"message": "Dil tarayıcı ayarlarından otomatik olarak algılanacak", "description": "Default language info message"}, "options_languageAutoInfo": {"message": "Dil tarayıcı ayarlarından otomatik olarak algılanacak", "description": "Language info when auto-detection is enabled"}, "options_languageManualInfo": {"message": "<PERSON><PERSON><PERSON> ettiğiniz dili manuel o<PERSON> se<PERSON>", "description": "Language info when manual selection is enabled"}, "options_saveSettings": {"message": "Ayarları Kaydet", "description": "Save settings button text"}, "options_settingsSaved": {"message": "<PERSON><PERSON><PERSON> başar<PERSON>yla kaydedildi!", "description": "Settings saved confirmation message"}, "content_exportButton": {"message": "<PERSON><PERSON> <PERSON>ışa Aktar", "description": "Content script export button text"}, "content_exportProgress": {"message": "Dışa aktarılıyor...", "description": "Content script export progress text"}, "content_exportComplete": {"message": "Dışa Aktarma Tamamlandı!", "description": "Content script export complete text"}, "content_exportError": {"message": "Dışa Aktarma Başarısız", "description": "Content script export error text"}, "content_loadingBookmarks": {"message": "Yer imleri yükleniyor...", "description": "Content script loading bookmarks text"}, "content_bookmarksFound": {"message": "$1 yer imi bulundu", "description": "Content script bookmarks found text", "placeholders": {"count": {"content": "$1", "example": "150"}}}, "content_noBookmarks": {"message": "<PERSON>r imi bulu<PERSON>ı", "description": "Content script no bookmarks text"}, "content_clickToExport": {"message": "<PERSON>r imlerinizi dışa aktarmak için tıklayın", "description": "Content script click to export text"}, "content_exportInProgress": {"message": "Dışa aktarma devam ediyor...", "description": "Content script export in progress text"}, "content_pleaseWait": {"message": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>in...", "description": "Content script please wait text"}, "content_cancel": {"message": "İptal", "description": "Content script cancel button text"}, "content_retry": {"message": "<PERSON><PERSON><PERSON>", "description": "Content script retry button text"}, "content_close": {"message": "Ka<PERSON><PERSON>", "description": "Content script close button text"}, "content_error_networkError": {"message": "<PERSON><PERSON> hatası oluştu", "description": "Content script network error message"}, "content_error_authError": {"message": "Kimlik doğrulama hatası", "description": "Content script auth error message"}, "content_error_rateLimit": {"message": "<PERSON><PERSON>z sınırı aşıldı", "description": "Content script rate limit error message"}, "content_error_unknown": {"message": "Bilinmeyen hata oluştu", "description": "Content script unknown error message"}, "content_status_initializing": {"message": "Başlatılıyor...", "description": "Content script initializing status"}, "content_status_connecting": {"message": "Bağlanıyor...", "description": "Content script connecting status"}, "content_status_processing": {"message": "Yer imleri işleniyor...", "description": "Content script processing status"}, "content_status_downloading": {"message": "Veri indiriliyor...", "description": "Content script downloading status"}, "content_status_complete": {"message": "Dışa aktarma başarıyla tamamlandı", "description": "Content script complete status"}, "privacyPolicy": {"message": "Gizlilik Politikası", "description": "Gizlilik politikasına giden bağlantı."}}