{"appName": {"message": "AbstractBookmark", "description": "The name of the extension"}, "appDescription": {"message": "Advanced bookmark management and export tool for social media platforms", "description": "The description of the extension"}, "popup_detectingPlatform": {"message": "Detecting platform...", "description": "Status message when detecting the current platform"}, "popup_navigateToBookmarks": {"message": "Navigate to your Twitter/X bookmarks page to export your saved tweets.", "description": "Message shown when user is not on bookmarks page"}, "popup_openTwitterBookmarks": {"message": "Open Twitter Bookmarks →", "description": "Link text to open Twitter bookmarks page"}, "popup_bookmarksDetected": {"message": "bookmarks detected", "description": "Label for bookmark count"}, "popup_downloadAllBookmarks": {"message": "Download All Bookmarks", "description": "Main export button text"}, "popup_exportAsJson": {"message": "Export as JSON with full metadata", "description": "Subtitle for JSON export option"}, "popup_preparingExport": {"message": "Preparing export...", "description": "Initial progress message"}, "popup_cancel": {"message": "Cancel", "description": "Cancel button text"}, "popup_exportOptions": {"message": "Export Options", "description": "Export options section title"}, "popup_exportFormat": {"message": "Export Format:", "description": "Export format label"}, "popup_formatJson": {"message": "JSON - Full metadata", "description": "JSON format option"}, "popup_formatExcel": {"message": "Excel - Spreadsheet format", "description": "Excel format option"}, "popup_formatCsv": {"message": "CSV - Comma separated", "description": "CSV format option"}, "popup_formatHtml": {"message": "HTML - Web page", "description": "HTML format option"}, "popup_includeMedia": {"message": "Include media URLs and metadata", "description": "Include media option"}, "popup_includeMetrics": {"message": "Include engagement metrics", "description": "Include metrics option"}, "popup_expandUrls": {"message": "Expand shortened URLs", "description": "Expand URLs option"}, "popup_exportCompleted": {"message": "Export completed successfully!", "description": "Success message when export is completed"}, "popup_settings": {"message": "Settings", "description": "Settings button text"}, "popup_help": {"message": "Help", "description": "Help button text"}, "status_onTwitterGoToBookmarks": {"message": "On Twitter - Go to bookmarks", "description": "Status when on Twitter but not on bookmarks page"}, "status_unsupportedPlatform": {"message": "Unsupported platform", "description": "Status when on unsupported platform"}, "status_twitterBookmarksDetected": {"message": "Twitter bookmarks detected", "description": "Status when on Twitter bookmarks page"}, "status_credentialsReady": {"message": "(Credentials Ready)", "description": "Status when credentials are available"}, "status_loading": {"message": "(Loading...)", "description": "Status when loading"}, "error_unableToDetectPage": {"message": "Unable to detect current page", "description": "Error when unable to detect current page"}, "error_twitterNotDetected": {"message": "Twitter/X not detected", "description": "Error when Twitter is not detected"}, "error_detectingPlatform": {"message": "Error detecting platform", "description": "Error when platform detection fails"}, "export_formatJson": {"message": "Export as JSON with full metadata", "description": "JSON export format description"}, "export_formatExcel": {"message": "Export as Excel spreadsheet", "description": "Excel export format description"}, "export_formatCsv": {"message": "Export as CSV file", "description": "CSV export format description"}, "export_formatHtml": {"message": "Export as HTML web page", "description": "HTML export format description"}, "progress_initializingExport": {"message": "Initializing export...", "description": "Progress message when initializing export"}, "progress_checkingCredentials": {"message": "Checking credentials...", "description": "Progress message when checking credentials"}, "progress_configuringOptions": {"message": "Configuring export options...", "description": "Progress message when configuring options"}, "progress_startingExport": {"message": "Starting export...", "description": "Progress message when starting export"}, "progress_exportStarted": {"message": "Export started successfully!", "description": "Progress message when export started"}, "error_credentialsNotReady": {"message": "Twitter credentials not ready. Please:\n1. Make sure you are logged in to Twitter/X\n2. Refresh this page and wait for it to load completely\n3. Try the export again", "description": "Error message when credentials are not ready"}, "error_failedToStartExport": {"message": "Failed to start export", "description": "Error message when export fails to start"}, "error_exportFailed": {"message": "Export failed: ", "description": "Error message prefix when export fails"}, "error_unknownError": {"message": "Unknown error", "description": "Error message for unknown errors"}, "error_exportCancelled": {"message": "Export cancelled by user", "description": "Message when export is cancelled by user"}, "success_exportCompleted": {"message": "Export completed! $COUNT$ bookmarks exported.", "description": "Success message when export is completed", "placeholders": {"count": {"content": "$1", "example": "150"}}}, "button_tryExtractCredentials": {"message": "Try Extract Credentials", "description": "Button text to try extracting credentials"}, "button_extracting": {"message": "Extracting...", "description": "Button text when extracting credentials"}, "button_credentialsExtracted": {"message": "✅ Credentials Extracted!", "description": "Button text when credentials are extracted successfully"}, "button_extractionFailed": {"message": "❌ Extraction Failed", "description": "Button text when credential extraction fails"}, "button_error": {"message": "❌ Error", "description": "Button text when an error occurs"}, "help_credentialsNotReady": {"message": "<strong>Credentials not ready:</strong><br>1. Refresh this Twitter page<br>2. Wait for page to load completely<br>3. Try export again", "description": "Help message when credentials are not ready"}, "options_title": {"message": "AbstractBookmark Settings", "description": "Options page title"}, "options_exportOptionsGroup": {"message": "Export Options", "description": "Export options group title"}, "options_performanceGroup": {"message": "Performance", "description": "Performance options group title"}, "options_privacyGroup": {"message": "Privacy", "description": "Privacy options group title"}, "options_languageGroup": {"message": "Language", "description": "Language options group title"}, "options_includeMedia": {"message": "Include media URLs and metadata", "description": "Include media option in settings"}, "options_includeMetrics": {"message": "Include engagement metrics (likes, retweets, etc.)", "description": "Include metrics option in settings"}, "options_expandUrls": {"message": "Expand shortened URLs", "description": "Expand URLs option in settings"}, "options_enableRateLimit": {"message": "Enable rate limiting protection", "description": "Rate limiting option in settings"}, "options_enableRetry": {"message": "Enable automatic retry on errors", "description": "Retry option in settings"}, "options_clearCredentials": {"message": "Clear credentials after export", "description": "Clear credentials option in settings"}, "options_anonymizeData": {"message": "Anonymize exported data", "description": "Anonymize data option in settings"}, "options_autoDetectLanguage": {"message": "Automatically detect language from browser settings", "description": "Auto-detect language option in settings"}, "options_selectLanguage": {"message": "Select Language:", "description": "Language selection label"}, "options_languageInfo": {"message": "Language will be automatically detected from your browser settings", "description": "Default language info message"}, "options_languageAutoInfo": {"message": "Language will be automatically detected from your browser settings", "description": "Language info when auto-detection is enabled"}, "options_languageManualInfo": {"message": "Select your preferred language manually", "description": "Language info when manual selection is enabled"}, "options_saveSettings": {"message": "Save Settings", "description": "Save settings button text"}, "options_settingsSaved": {"message": "Setting<PERSON> saved successfully!", "description": "Settings saved confirmation message"}, "background_exportStarted": {"message": "Export started successfully", "description": "Background message when export starts"}, "background_exportCompleted": {"message": "Export completed successfully", "description": "Background message when export completes"}, "background_exportFailed": {"message": "Export failed", "description": "Background message when export fails"}, "background_credentialsNotFound": {"message": "Twitter credentials not found", "description": "Background message when credentials are missing"}, "background_invalidPlatform": {"message": "Invalid or unsupported platform", "description": "Background message for invalid platform"}, "background_exportInProgress": {"message": "Export already in progress", "description": "Background message when export is already running"}, "content_exportButton": {"message": "Export Bookmarks", "description": "Content script export button text"}, "content_exportProgress": {"message": "Exporting...", "description": "Content script export progress text"}, "content_exportComplete": {"message": "Export Complete!", "description": "Content script export complete text"}, "content_exportError": {"message": "Export Failed", "description": "Content script export error text"}, "content_loadingBookmarks": {"message": "Loading bookmarks...", "description": "Content script loading bookmarks text"}, "content_bookmarksFound": {"message": "$1 bookmarks found", "description": "Content script bookmarks found text", "placeholders": {"count": {"content": "$1", "example": "150"}}}, "content_noBookmarks": {"message": "No bookmarks found", "description": "Content script no bookmarks text"}, "content_clickToExport": {"message": "Click to export your bookmarks", "description": "Content script click to export text"}, "content_exportInProgress": {"message": "Export in progress...", "description": "Content script export in progress text"}, "content_pleaseWait": {"message": "Please wait...", "description": "Content script please wait text"}, "content_cancel": {"message": "Cancel", "description": "Content script cancel button text"}, "content_retry": {"message": "Retry", "description": "Content script retry button text"}, "content_close": {"message": "Close", "description": "Content script close button text"}, "content_error_networkError": {"message": "Network error occurred", "description": "Content script network error message"}, "content_error_authError": {"message": "Authentication error", "description": "Content script auth error message"}, "content_error_rateLimit": {"message": "Rate limit exceeded", "description": "Content script rate limit error message"}, "content_error_unknown": {"message": "Unknown error occurred", "description": "Content script unknown error message"}, "content_status_initializing": {"message": "Initializing...", "description": "Content script initializing status"}, "content_status_connecting": {"message": "Connecting...", "description": "Content script connecting status"}, "content_status_processing": {"message": "Processing bookmarks...", "description": "Content script processing status"}, "content_status_downloading": {"message": "Downloading data...", "description": "Content script downloading status"}, "content_status_complete": {"message": "Export completed successfully", "description": "Content script complete status"}, "privacyPolicy": {"message": "গোপনীয়তা নীতি", "description": "গোপনীয়তা নীতিতে লিঙ্ক করুন।"}}