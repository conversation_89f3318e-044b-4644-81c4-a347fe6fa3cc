{"appName": {"message": "AbstractBookmark", "description": "The name of the extension"}, "appDescription": {"message": "Outil avancé de gestion et d'exportation de signets pour les plateformes de médias sociaux", "description": "The description of the extension"}, "popup_detectingPlatform": {"message": "Détection de la plateforme...", "description": "Status message when detecting the current platform"}, "popup_navigateToBookmarks": {"message": "Naviguez vers votre page de signets Twitter/X pour exporter vos tweets sauvegardés.", "description": "Message shown when user is not on bookmarks page"}, "popup_openTwitterBookmarks": {"message": "Ouvrir les signets Twitter →", "description": "Link text to open Twitter bookmarks page"}, "popup_bookmarksDetected": {"message": "signets détectés", "description": "Label for bookmark count"}, "popup_downloadAllBookmarks": {"message": "Télécharger tous les signets", "description": "Main export button text"}, "popup_exportAsJson": {"message": "Exporter en JSON avec métadonnées complètes", "description": "Subtitle for JSON export option"}, "popup_preparingExport": {"message": "Préparation de l'exportation...", "description": "Initial progress message"}, "popup_cancel": {"message": "Annuler", "description": "Cancel button text"}, "popup_exportOptions": {"message": "Options d'exportation", "description": "Export options section title"}, "popup_exportFormat": {"message": "Format d'exportation :", "description": "Export format label"}, "popup_formatJson": {"message": "JSON - Métadonnées complètes", "description": "JSON format option"}, "popup_formatExcel": {"message": "Excel - Format tableur", "description": "Excel format option"}, "popup_formatCsv": {"message": "CSV - Séparé par virgules", "description": "CSV format option"}, "popup_formatHtml": {"message": "HTML - Page web", "description": "HTML format option"}, "popup_includeMedia": {"message": "Inclure les URLs de médias et métadonnées", "description": "Include media option"}, "popup_includeMetrics": {"message": "Inclure les métriques d'engagement", "description": "Include metrics option"}, "popup_expandUrls": {"message": "Développer les URLs raccourcies", "description": "Expand URLs option"}, "popup_exportCompleted": {"message": "Exportation terminée avec succès !", "description": "Success message when export is completed"}, "popup_settings": {"message": "Paramètres", "description": "Settings button text"}, "popup_help": {"message": "Aide", "description": "Help button text"}, "status_onTwitterGoToBookmarks": {"message": "Sur Twitter - Aller aux signets", "description": "Status when on Twitter but not on bookmarks page"}, "status_unsupportedPlatform": {"message": "Plateforme non supportée", "description": "Status when on unsupported platform"}, "status_twitterBookmarksDetected": {"message": "Signets Twitter détectés", "description": "Status when on Twitter bookmarks page"}, "status_credentialsReady": {"message": "(Identifiants prêts)", "description": "Status when credentials are available"}, "status_loading": {"message": "(Chargement...)", "description": "Status when loading"}, "error_unableToDetectPage": {"message": "Impossible de détecter la page actuelle", "description": "Error when unable to detect current page"}, "error_twitterNotDetected": {"message": "Twitter/X non détecté", "description": "Error when Twitter is not detected"}, "error_detectingPlatform": {"message": "Erreur lors de la détection de la plateforme", "description": "Error when platform detection fails"}, "export_formatJson": {"message": "Exporter en JSON avec métadonnées complètes", "description": "JSON export format description"}, "export_formatExcel": {"message": "Exporter en feuille de calcul Excel", "description": "Excel export format description"}, "export_formatCsv": {"message": "Exporter en fichier CSV", "description": "CSV export format description"}, "export_formatHtml": {"message": "Exporter en page web HTML", "description": "HTML export format description"}, "progress_initializingExport": {"message": "Initialisation de l'exportation...", "description": "Progress message when initializing export"}, "progress_checkingCredentials": {"message": "Vérification des identifiants...", "description": "Progress message when checking credentials"}, "progress_configuringOptions": {"message": "Configuration des options d'exportation...", "description": "Progress message when configuring options"}, "progress_startingExport": {"message": "Démarrage de l'exportation...", "description": "Progress message when starting export"}, "progress_exportStarted": {"message": "Exportation démarrée avec succès !", "description": "Progress message when export started"}, "error_credentialsNotReady": {"message": "Les identifiants Twitter ne sont pas prêts. Veuillez :\n1. Vous assurer d'être connecté à Twitter/X\n2. Actualiser cette page et attendre qu'elle se charge complètement\n3. Réessayer l'exportation", "description": "Error message when credentials are not ready"}, "error_failedToStartExport": {"message": "Échec du démarrage de l'exportation", "description": "Error message when export fails to start"}, "error_exportFailed": {"message": "Exportation échouée : ", "description": "Error message prefix when export fails"}, "error_unknownError": {"message": "<PERSON><PERSON><PERSON> inconnue", "description": "Error message for unknown errors"}, "error_exportCancelled": {"message": "Exportation annulée par l'utilisateur", "description": "Message when export is cancelled by user"}, "success_exportCompleted": {"message": "Exportation terminée ! $COUNT$ signets exportés.", "description": "Success message when export is completed", "placeholders": {"count": {"content": "$1", "example": "150"}}}, "button_tryExtractCredentials": {"message": "Essayer d'extraire les identifiants", "description": "Button text to try extracting credentials"}, "button_extracting": {"message": "Extraction...", "description": "Button text when extracting credentials"}, "button_credentialsExtracted": {"message": "✅ Identifiants extraits !", "description": "Button text when credentials are extracted successfully"}, "button_extractionFailed": {"message": "❌ Extraction échouée", "description": "Button text when credential extraction fails"}, "button_error": {"message": "❌ E<PERSON>ur", "description": "Button text when an error occurs"}, "help_credentialsNotReady": {"message": "<strong>Identifiants non prêts :</strong><br>1. Actualiser cette page Twitter<br>2. Attendre que la page se charge complètement<br>3. Réessayer l'exportation", "description": "Help message when credentials are not ready"}, "options_title": {"message": "Paramètres AbstractBookmark", "description": "Options page title"}, "options_exportOptionsGroup": {"message": "Options d'exportation", "description": "Export options group title"}, "options_performanceGroup": {"message": "Performance", "description": "Performance options group title"}, "options_privacyGroup": {"message": "Confidentialité", "description": "Privacy options group title"}, "options_languageGroup": {"message": "<PERSON><PERSON>", "description": "Language options group title"}, "options_includeMedia": {"message": "Inclure les URLs de médias et métadonnées", "description": "Include media option in settings"}, "options_includeMetrics": {"message": "Inclure les métriques d'engagement (likes, retweets, etc.)", "description": "Include metrics option in settings"}, "options_expandUrls": {"message": "Développer les URLs raccourcies", "description": "Expand URLs option in settings"}, "options_enableRateLimit": {"message": "Activer la protection de limite de débit", "description": "Rate limiting option in settings"}, "options_enableRetry": {"message": "Activer la nouvelle tentative automatique en cas d'erreur", "description": "Retry option in settings"}, "options_clearCredentials": {"message": "Effacer les identifiants après exportation", "description": "Clear credentials option in settings"}, "options_anonymizeData": {"message": "Anonymiser les données exportées", "description": "Anonymize data option in settings"}, "options_autoDetectLanguage": {"message": "Détecter automatiquement la langue depuis les paramètres du navigateur", "description": "Auto-detect language option in settings"}, "options_selectLanguage": {"message": "Sélectionner la langue :", "description": "Language selection label"}, "options_languageInfo": {"message": "La langue sera automatiquement détectée depuis les paramètres de votre navigateur", "description": "Default language info message"}, "options_languageAutoInfo": {"message": "La langue sera automatiquement détectée depuis les paramètres de votre navigateur", "description": "Language info when auto-detection is enabled"}, "options_languageManualInfo": {"message": "Sélectionnez votre langue préférée manuellement", "description": "Language info when manual selection is enabled"}, "options_saveSettings": {"message": "Sauvegarder les paramètres", "description": "Save settings button text"}, "options_settingsSaved": {"message": "Paramètres sauvegardés avec succès !", "description": "Settings saved confirmation message"}, "background_exportStarted": {"message": "Exportation démarrée avec succès", "description": "Background message when export starts"}, "background_exportCompleted": {"message": "Exportation terminée avec succès", "description": "Background message when export completes"}, "background_exportFailed": {"message": "Exportation échouée", "description": "Background message when export fails"}, "background_credentialsNotFound": {"message": "Identifiants Twitter non trouvés", "description": "Background message when credentials are missing"}, "background_invalidPlatform": {"message": "Plateforme invalide ou non supportée", "description": "Background message for invalid platform"}, "background_exportInProgress": {"message": "Exportation déjà en cours", "description": "Background message when export is already running"}, "content_exportButton": {"message": "Exporter les signets", "description": "Content script export button text"}, "content_exportProgress": {"message": "Exportation...", "description": "Content script export progress text"}, "content_exportComplete": {"message": "Exportation terminée !", "description": "Content script export complete text"}, "content_exportError": {"message": "Exportation échouée", "description": "Content script export error text"}, "content_loadingBookmarks": {"message": "Chargement des signets...", "description": "Content script loading bookmarks text"}, "content_bookmarksFound": {"message": "$1 signets trouvés", "description": "Content script bookmarks found text", "placeholders": {"count": {"content": "$1", "example": "150"}}}, "content_noBookmarks": {"message": "<PERSON><PERSON><PERSON> signet trouvé", "description": "Content script no bookmarks text"}, "content_clickToExport": {"message": "Cliquez pour exporter vos signets", "description": "Content script click to export text"}, "content_exportInProgress": {"message": "Exportation en cours...", "description": "Content script export in progress text"}, "content_pleaseWait": {"message": "Veuillez patienter...", "description": "Content script please wait text"}, "content_cancel": {"message": "Annuler", "description": "Content script cancel button text"}, "content_retry": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "Content script retry button text"}, "content_close": {"message": "<PERSON><PERSON><PERSON>", "description": "Content script close button text"}, "content_error_networkError": {"message": "<PERSON><PERSON><PERSON> r<PERSON><PERSON> survenue", "description": "Content script network error message"}, "content_error_authError": {"message": "Erreur d'authentification", "description": "Content script auth error message"}, "content_error_rateLimit": {"message": "Limite de débit dépassée", "description": "Content script rate limit error message"}, "content_error_unknown": {"message": "Erreur inconnue survenue", "description": "Content script unknown error message"}, "content_status_initializing": {"message": "Initialisation...", "description": "Content script initializing status"}, "content_status_connecting": {"message": "Connexion...", "description": "Content script connecting status"}, "content_status_processing": {"message": "Traitement des signets...", "description": "Content script processing status"}, "content_status_downloading": {"message": "Téléchargement des données...", "description": "Content script downloading status"}, "content_status_complete": {"message": "Exportation terminée avec succès", "description": "Content script complete status"}, "privacyPolicy": {"message": "Politique de Confidentialité", "description": "Lien vers la politique de confidentialité."}}