/**
 * Export Manager - Handles bookmark export operations
 */

import { Logger } from './logger.js';
import { StorageManager } from './storage-manager.js';

export class ExportManager {
  constructor() {
    this.logger = new Logger('ExportManager');
    this.storageManager = new StorageManager();
    this.activeExports = new Map();
  }

  async startExport(platform, options) {
    const exportId = this.generateExportId();

    const exportData = {
      id: exportId,
      platform,
      options,
      status: 'initializing',
      createdAt: new Date().toISOString(),
      progress: 0,
      totalBookmarks: 0,
      processedBookmarks: 0,
      error: null,
    };

    this.activeExports.set(exportId, exportData);
    await this.storageManager.saveExportData(exportId, exportData);

    this.logger.info(`Export started: ${exportId} for platform ${platform}`);
    return exportId;
  }

  async updateExportStatus(exportId, status, error = null) {
    try {
      const exportData =
        this.activeExports.get(exportId) ||
        (await this.storageManager.getExportData(exportId));

      if (!exportData) {
        throw new Error(`Export not found: ${exportId}`);
      }

      exportData.status = status;
      exportData.updatedAt = new Date().toISOString();

      if (error) {
        exportData.error = error;
      }

      if (status === 'completed' || status === 'failed') {
        exportData.completedAt = new Date().toISOString();
      }

      this.activeExports.set(exportId, exportData);
      await this.storageManager.saveExportData(exportId, exportData);

      this.logger.debug(`Export ${exportId} status updated to: ${status}`);
    } catch (error) {
      this.logger.error(
        `Failed to update export status for ${exportId}:`,
        error
      );
      throw error;
    }
  }

  async getExportProgress(exportId) {
    try {
      const exportData =
        this.activeExports.get(exportId) ||
        (await this.storageManager.getExportData(exportId));

      if (!exportData) {
        throw new Error(`Export not found: ${exportId}`);
      }

      return {
        id: exportData.id,
        status: exportData.status,
        progress: exportData.progress,
        totalBookmarks: exportData.totalBookmarks,
        processedBookmarks: exportData.processedBookmarks,
        createdAt: exportData.createdAt,
        updatedAt: exportData.updatedAt,
        completedAt: exportData.completedAt,
        error: exportData.error,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get export progress for ${exportId}:`,
        error
      );
      throw error;
    }
  }

  async processBookmarks(exportId, bookmarks, options) {
    try {
      this.logger.info(
        `Processing ${bookmarks.length} bookmarks for export ${exportId}`
      );

      const exportData = this.activeExports.get(exportId);
      if (!exportData) {
        throw new Error(`Export not found: ${exportId}`);
      }

      exportData.totalBookmarks = bookmarks.length;
      exportData.status = 'processing';
      await this.storageManager.saveExportData(exportId, exportData);

      // Apply filters
      const filteredBookmarks = this.applyFilters(bookmarks, options);
      this.logger.info(`Filtered to ${filteredBookmarks.length} bookmarks`);

      // Process bookmarks in batches
      const batchSize = options.batchSize || 100;
      const processedBookmarks = [];

      for (let i = 0; i < filteredBookmarks.length; i += batchSize) {
        const batch = filteredBookmarks.slice(i, i + batchSize);
        const processedBatch = await this.processBatch(batch, options);

        processedBookmarks.push(...processedBatch);

        // Update progress
        exportData.processedBookmarks = processedBookmarks.length;
        exportData.progress =
          (processedBookmarks.length / filteredBookmarks.length) * 100;

        this.activeExports.set(exportId, exportData);
        await this.storageManager.saveExportData(exportId, exportData);

        // Small delay to prevent blocking
        await this.sleep(10);
      }

      // Generate export file
      const exportResult = await this.generateExportFile(
        processedBookmarks,
        options
      );

      exportData.result = exportResult;
      exportData.processedBookmarks = processedBookmarks.length;
      exportData.progress = 100;

      this.activeExports.set(exportId, exportData);
      await this.storageManager.saveExportData(exportId, exportData);

      this.logger.info(
        `Export ${exportId} processing completed: ${processedBookmarks.length} bookmarks`
      );
      return exportResult;
    } catch (error) {
      this.logger.error(
        `Failed to process bookmarks for export ${exportId}:`,
        error
      );
      throw error;
    }
  }

  applyFilters(bookmarks, options) {
    let filtered = [...bookmarks];

    // Date range filter
    if (options.startDate || options.endDate) {
      const startDate = options.startDate
        ? new Date(options.startDate)
        : new Date(0);
      const endDate = options.endDate ? new Date(options.endDate) : new Date();

      filtered = filtered.filter((bookmark) => {
        const createdAt = new Date(
          bookmark.content?.createdAt || bookmark.bookmarkedAt
        );
        return createdAt >= startDate && createdAt <= endDate;
      });
    }

    // Author filter
    if (options.authors && options.authors.length > 0) {
      filtered = filtered.filter((bookmark) =>
        options.authors.includes(bookmark.author?.username)
      );
    }

    // Content type filter
    if (options.contentTypes && options.contentTypes.length > 0) {
      filtered = filtered.filter((bookmark) => {
        if (options.contentTypes.includes('text') && bookmark.content?.text)
          return true;
        if (
          options.contentTypes.includes('media') &&
          bookmark.media?.length > 0
        )
          return true;
        if (options.contentTypes.includes('links') && bookmark.urls?.length > 0)
          return true;
        return false;
      });
    }

    // Keyword filter
    if (options.keywords && options.keywords.length > 0) {
      filtered = filtered.filter((bookmark) => {
        const text = bookmark.content?.text?.toLowerCase() || '';
        return options.keywords.some((keyword) =>
          text.includes(keyword.toLowerCase())
        );
      });
    }

    return filtered;
  }

  async processBatch(bookmarks, options) {
    const processed = [];

    for (const bookmark of bookmarks) {
      try {
        const processedBookmark = await this.processBookmark(bookmark, options);
        processed.push(processedBookmark);
      } catch (error) {
        this.logger.warn(`Failed to process bookmark ${bookmark.id}:`, error);

        // Include original bookmark with error flag if processing fails
        processed.push({
          ...bookmark,
          processingError: error.message,
        });
      }
    }

    return processed;
  }

  async processBookmark(bookmark, options) {
    const processed = { ...bookmark };

    // Add processing timestamp
    processed.processedAt = new Date().toISOString();

    // Enrich with additional metadata if requested
    if (options.includeMetadata) {
      processed.metadata = {
        wordCount: this.countWords(bookmark.content?.text || ''),
        hasMedia: (bookmark.media?.length || 0) > 0,
        hasLinks: (bookmark.urls?.length || 0) > 0,
        language: bookmark.content?.lang || 'unknown',
      };
    }

    // Download media if requested
    if (options.includeMedia && bookmark.media?.length > 0) {
      processed.media = await this.processMedia(bookmark.media, options);
    }

    // Process URLs if requested
    if (options.expandUrls && bookmark.urls?.length > 0) {
      processed.urls = await this.processUrls(bookmark.urls, options);
    }

    return processed;
  }

  async processMedia(mediaItems, options) {
    // For now, just return the media items as-is
    // In a full implementation, this could download and store media files
    return mediaItems.map((media) => ({
      ...media,
      processed: true,
      processedAt: new Date().toISOString(),
    }));
  }

  async processUrls(urlItems, options) {
    // For now, just return the URLs as-is
    // In a full implementation, this could expand shortened URLs
    return urlItems.map((url) => ({
      ...url,
      processed: true,
      processedAt: new Date().toISOString(),
    }));
  }

  async generateExportFile(bookmarks, options) {
    const format = options.exportFormat || 'json';

    switch (format.toLowerCase()) {
      case 'json':
        return this.generateJSONExport(bookmarks, options);
      case 'csv':
        return this.generateCSVExport(bookmarks, options);
      case 'excel':
      case 'xlsx':
        return this.generateExcelExport(bookmarks, options);
      case 'html':
        return this.generateHTMLExport(bookmarks, options);
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  }

  generateJSONExport(bookmarks, options) {
    const exportData = {
      metadata: {
        exportedAt: new Date().toISOString(),
        totalBookmarks: bookmarks.length,
        format: 'json',
        version: '1.0.0',
        options: options,
      },
      bookmarks: bookmarks,
    };

    const jsonString = JSON.stringify(exportData, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });

    return {
      format: 'json',
      filename: `bookmarks_${new Date().toISOString().split('T')[0]}.json`,
      size: blob.size,
      data: jsonString,
      blob: blob,
    };
  }

  generateCSVExport(bookmarks, options) {
    const headers = [
      'ID',
      'Platform',
      'Author Username',
      'Author Display Name',
      'Content',
      'Created At',
      'Bookmarked At',
      'Retweets',
      'Likes',
      'Replies',
      'Quotes',
      'Media Count',
      'URL Count',
    ];

    const rows = bookmarks.map((bookmark) => [
      bookmark.id || '',
      bookmark.platform || '',
      bookmark.author?.username || '',
      bookmark.author?.displayName || '',
      (bookmark.content?.text || '').replace(/"/g, '""'),
      bookmark.content?.createdAt || '',
      bookmark.bookmarkedAt || '',
      bookmark.metrics?.retweets || 0,
      bookmark.metrics?.likes || 0,
      bookmark.metrics?.replies || 0,
      bookmark.metrics?.quotes || 0,
      bookmark.media?.length || 0,
      bookmark.urls?.length || 0,
    ]);

    const csvContent = [
      headers.join(','),
      ...rows.map((row) => row.map((cell) => `"${cell}"`).join(',')),
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });

    return {
      format: 'csv',
      filename: `bookmarks_${new Date().toISOString().split('T')[0]}.csv`,
      size: blob.size,
      data: csvContent,
      blob: blob,
    };
  }

  generateExcelExport(bookmarks, options) {
    // Create Excel XML format (compatible with Excel and other spreadsheet apps)
    const headers = [
      'ID',
      'Platform',
      'Author Username',
      'Author Display Name',
      'Content',
      'Created At',
      'Bookmarked At',
      'Retweets',
      'Likes',
      'Replies',
      'Quotes',
      'Media Count',
      'URL Count',
      'Language',
      'Has Media',
      'Has Links',
    ];

    // Prepare data rows
    const rows = bookmarks.map((bookmark) => [
      bookmark.id || '',
      bookmark.platform || '',
      bookmark.author?.username || '',
      bookmark.author?.displayName || '',
      (bookmark.content?.text || '').replace(/"/g, '""').replace(/\n/g, ' '),
      bookmark.content?.createdAt || '',
      bookmark.bookmarkedAt || '',
      bookmark.metrics?.retweets || 0,
      bookmark.metrics?.likes || 0,
      bookmark.metrics?.replies || 0,
      bookmark.metrics?.quotes || 0,
      bookmark.media?.length || 0,
      bookmark.urls?.length || 0,
      bookmark.content?.lang || 'unknown',
      (bookmark.media?.length || 0) > 0 ? 'Yes' : 'No',
      (bookmark.urls?.length || 0) > 0 ? 'Yes' : 'No',
    ]);

    // Create Excel XML content
    const excelXML = `<?xml version="1.0"?>
<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:o="urn:schemas-microsoft-com:office:office"
 xmlns:x="urn:schemas-microsoft-com:office:excel"
 xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:html="http://www.w3.org/TR/REC-html40">
 <DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">
  <Title>Exported Bookmarks</Title>
  <Author>AbstractBookmark</Author>
  <Created>${new Date().toISOString()}</Created>
 </DocumentProperties>
 <Styles>
  <Style ss:ID="Header">
   <Font ss:Bold="1"/>
   <Interior ss:Color="#E0E0E0" ss:Pattern="Solid"/>
  </Style>
 </Styles>
 <Worksheet ss:Name="Bookmarks">
  <Table>
   <Row ss:StyleID="Header">
    ${headers.map((header) => `<Cell><Data ss:Type="String">${header}</Data></Cell>`).join('')}
   </Row>
   ${rows
     .map(
       (row) => `<Row>
    ${row
      .map((cell, index) => {
        const isNumber = index >= 7 && index <= 12; // Numeric columns
        const type = isNumber ? 'Number' : 'String';
        const value = String(cell)
          .replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;');
        return `<Cell><Data ss:Type="${type}">${value}</Data></Cell>`;
      })
      .join('')}
   </Row>`
     )
     .join('')}
  </Table>
 </Worksheet>
 <Worksheet ss:Name="Summary">
  <Table>
   <Row ss:StyleID="Header">
    <Cell><Data ss:Type="String">Metric</Data></Cell>
    <Cell><Data ss:Type="String">Value</Data></Cell>
   </Row>
   <Row><Cell><Data ss:Type="String">Total Bookmarks</Data></Cell><Cell><Data ss:Type="Number">${bookmarks.length}</Data></Cell></Row>
   <Row><Cell><Data ss:Type="String">Export Date</Data></Cell><Cell><Data ss:Type="String">${new Date().toISOString()}</Data></Cell></Row>
   <Row><Cell><Data ss:Type="String">Export Format</Data></Cell><Cell><Data ss:Type="String">Excel (XML)</Data></Cell></Row>
   <Row><Cell><Data ss:Type="String">Total Likes</Data></Cell><Cell><Data ss:Type="Number">${bookmarks.reduce((sum, b) => sum + (b.metrics?.likes || 0), 0)}</Data></Cell></Row>
   <Row><Cell><Data ss:Type="String">Total Retweets</Data></Cell><Cell><Data ss:Type="Number">${bookmarks.reduce((sum, b) => sum + (b.metrics?.retweets || 0), 0)}</Data></Cell></Row>
   <Row><Cell><Data ss:Type="String">Total Replies</Data></Cell><Cell><Data ss:Type="Number">${bookmarks.reduce((sum, b) => sum + (b.metrics?.replies || 0), 0)}</Data></Cell></Row>
   <Row><Cell><Data ss:Type="String">Bookmarks with Media</Data></Cell><Cell><Data ss:Type="Number">${bookmarks.filter((b) => (b.media?.length || 0) > 0).length}</Data></Cell></Row>
   <Row><Cell><Data ss:Type="String">Bookmarks with Links</Data></Cell><Cell><Data ss:Type="Number">${bookmarks.filter((b) => (b.urls?.length || 0) > 0).length}</Data></Cell></Row>
  </Table>
 </Worksheet>
</Workbook>`;

    const blob = new Blob([excelXML], {
      type: 'application/vnd.ms-excel',
    });

    return {
      format: 'excel',
      filename: `bookmarks_${new Date().toISOString().split('T')[0]}.xls`,
      size: blob.size,
      data: excelXML,
      blob: blob,
    };
  }

  generateHTMLExport(bookmarks, options) {
    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exported Bookmarks</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .bookmark { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
        .author { font-weight: bold; color: #1da1f2; }
        .content { margin: 10px 0; }
        .metadata { font-size: 0.9em; color: #666; }
        .metrics { margin-top: 10px; }
        .metric { display: inline-block; margin-right: 15px; }
    </style>
</head>
<body>
    <h1>Exported Bookmarks</h1>
    <p>Exported on: ${new Date().toISOString()}</p>
    <p>Total bookmarks: ${bookmarks.length}</p>
    
    ${bookmarks
      .map(
        (bookmark) => `
        <div class="bookmark">
            <div class="author">@${bookmark.author?.username || 'unknown'} (${bookmark.author?.displayName || 'Unknown User'})</div>
            <div class="content">${bookmark.content?.text || 'No content'}</div>
            <div class="metadata">
                Platform: ${bookmark.platform} | 
                Created: ${bookmark.content?.createdAt || 'Unknown'} |
                Bookmarked: ${bookmark.bookmarkedAt || 'Unknown'}
            </div>
            <div class="metrics">
                <span class="metric">❤️ ${bookmark.metrics?.likes || 0}</span>
                <span class="metric">🔄 ${bookmark.metrics?.retweets || 0}</span>
                <span class="metric">💬 ${bookmark.metrics?.replies || 0}</span>
                <span class="metric">📎 ${bookmark.urls?.length || 0} links</span>
                <span class="metric">🖼️ ${bookmark.media?.length || 0} media</span>
            </div>
        </div>
    `
      )
      .join('')}
</body>
</html>`;

    const blob = new Blob([html], { type: 'text/html' });

    return {
      format: 'html',
      filename: `bookmarks_${new Date().toISOString().split('T')[0]}.html`,
      size: blob.size,
      data: html,
      blob: blob,
    };
  }

  async cancelExport(exportId) {
    try {
      const exportData = this.activeExports.get(exportId);

      if (exportData) {
        exportData.status = 'cancelled';
        exportData.cancelledAt = new Date().toISOString();

        this.activeExports.set(exportId, exportData);
        await this.storageManager.saveExportData(exportId, exportData);
      }

      this.logger.info(`Export cancelled: ${exportId}`);
    } catch (error) {
      this.logger.error(`Failed to cancel export ${exportId}:`, error);
      throw error;
    }
  }

  async cleanupOldExports(maxAge = 7) {
    try {
      this.logger.info(`Cleaning up exports older than ${maxAge} days`);

      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - maxAge);

      let cleanedCount = 0;

      // Clean up active exports
      for (const [exportId, exportData] of this.activeExports.entries()) {
        const createdAt = new Date(exportData.createdAt);

        if (createdAt < cutoffDate) {
          this.activeExports.delete(exportId);
          await this.storageManager.deleteExportData(exportId);
          cleanedCount++;
        }
      }

      this.logger.info(`Cleaned up ${cleanedCount} old exports`);
      return cleanedCount;
    } catch (error) {
      this.logger.error('Failed to cleanup old exports:', error);
      throw error;
    }
  }

  generateExportId() {
    return `export_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  countWords(text) {
    return text
      .trim()
      .split(/\s+/)
      .filter((word) => word.length > 0).length;
  }

  sleep(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
