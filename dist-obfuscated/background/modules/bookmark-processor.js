/**
 * Bookmark Data Processing Pipeline
 * Handles normalization, media extraction, and export functionality
 */

import { Logger } from './logger.js';

/**
 * Main bookmark processor class
 */
export class BookmarkProcessor {
  constructor() {
    this.logger = new Logger('BookmarkProcessor');
    this.mediaExtractor = new MediaExtractor();
    this.urlProcessor = new URLProcessor();
    this.dataValidator = new DataValidator();
  }

  /**
   * Process raw Twitter bookmark data into normalized format
   */
  async processTwitterBookmarks(rawBookmarks, options = {}) {
    this.logger.info(`Processing ${rawBookmarks.length} Twitter bookmarks`);
    
    const processedBookmarks = [];
    const errors = [];

    for (let i = 0; i < rawBookmarks.length; i++) {
      try {
        const processed = await this.processTwitterBookmark(rawBookmarks[i], options);
        if (processed) {
          processedBookmarks.push(processed);
        }
      } catch (error) {
        this.logger.error(`Error processing bookmark ${i}:`, error);
        errors.push({ index: i, error: error.message });
      }
    }

    this.logger.info(`Successfully processed ${processedBookmarks.length} bookmarks, ${errors.length} errors`);
    
    return {
      bookmarks: processedBookmarks,
      errors: errors,
      metadata: {
        totalProcessed: processedBookmarks.length,
        totalErrors: errors.length,
        processedAt: new Date().toISOString(),
        options: options
      }
    };
  }

  /**
   * Process a single Twitter bookmark
   */
  async processTwitterBookmark(rawBookmark, options) {
    // Extract tweet data from GraphQL response structure
    const tweet = this.extractTweetFromEntry(rawBookmark);
    if (!tweet) {
      throw new Error('Invalid bookmark entry structure');
    }

    // Build normalized bookmark object
    const bookmark = {
      id: tweet.rest_id,
      platform: 'twitter',
      url: `https://twitter.com/i/web/status/${tweet.rest_id}`,
      
      // Author information
      author: await this.extractAuthorInfo(tweet),
      
      // Content information
      content: await this.extractContentInfo(tweet, options),
      
      // Media attachments
      media: options.includeMedia ? await this.mediaExtractor.extractMedia(tweet) : [],
      
      // Engagement metrics
      metrics: options.includeMetrics ? this.extractMetrics(tweet) : {},
      
      // URLs and links
      urls: options.expandUrls ? await this.urlProcessor.processUrls(tweet) : this.extractBasicUrls(tweet),
      
      // Metadata
      metadata: {
        bookmarkedAt: new Date().toISOString(),
        extractedAt: new Date().toISOString(),
        tweetType: this.determineTweetType(tweet),
        language: tweet.legacy?.lang || 'unknown'
      }
    };

    // Validate the processed bookmark
    this.dataValidator.validateBookmark(bookmark);
    
    return bookmark;
  }

  /**
   * Extract tweet object from GraphQL entry structure
   */
  extractTweetFromEntry(entry) {
    // Handle different entry types
    if (entry.content?.itemContent?.tweet_results?.result) {
      return entry.content.itemContent.tweet_results.result;
    }
    
    if (entry.content?.item?.content?.tweet?.content) {
      return entry.content.item.content.tweet.content;
    }
    
    // Handle promoted tweets
    if (entry.content?.itemContent?.promotedMetadata) {
      return entry.content.itemContent.tweet_results?.result;
    }
    
    return null;
  }

  /**
   * Extract author information
   */
  async extractAuthorInfo(tweet) {
    const user = tweet.core?.user_results?.result?.legacy;
    if (!user) {
      return {
        username: 'unknown',
        displayName: 'Unknown User',
        profileImage: null,
        verified: false,
        followersCount: 0
      };
    }

    return {
      id: tweet.core.user_results.result.rest_id,
      username: user.screen_name,
      displayName: user.name,
      description: user.description,
      profileImage: user.profile_image_url_https,
      profileBanner: user.profile_banner_url,
      verified: user.verified || false,
      followersCount: user.followers_count || 0,
      followingCount: user.friends_count || 0,
      tweetsCount: user.statuses_count || 0,
      location: user.location,
      website: user.url,
      createdAt: user.created_at
    };
  }

  /**
   * Extract content information
   */
  async extractContentInfo(tweet, options) {
    const legacy = tweet.legacy;
    if (!legacy) {
      return {
        text: '',
        createdAt: new Date().toISOString(),
        language: 'unknown'
      };
    }

    const content = {
      text: legacy.full_text || legacy.text || '',
      createdAt: legacy.created_at,
      language: legacy.lang || 'unknown',
      source: legacy.source,
      inReplyToStatusId: legacy.in_reply_to_status_id_str,
      inReplyToUserId: legacy.in_reply_to_user_id_str,
      inReplyToScreenName: legacy.in_reply_to_screen_name
    };

    // Extract hashtags and mentions
    if (legacy.entities) {
      content.hashtags = legacy.entities.hashtags?.map(tag => tag.text) || [];
      content.mentions = legacy.entities.user_mentions?.map(mention => ({
        id: mention.id_str,
        username: mention.screen_name,
        displayName: mention.name
      })) || [];
      content.symbols = legacy.entities.symbols?.map(symbol => symbol.text) || [];
    }

    // Handle quoted tweets
    if (tweet.quoted_status_result?.result) {
      content.quotedTweet = await this.processTwitterBookmark({
        content: {
          itemContent: {
            tweet_results: {
              result: tweet.quoted_status_result.result
            }
          }
        }
      }, { ...options, includeMedia: false }); // Avoid deep nesting
    }

    return content;
  }

  /**
   * Extract engagement metrics
   */
  extractMetrics(tweet) {
    const legacy = tweet.legacy;
    if (!legacy) return {};

    return {
      retweets: legacy.retweet_count || 0,
      likes: legacy.favorite_count || 0,
      replies: legacy.reply_count || 0,
      quotes: legacy.quote_count || 0,
      bookmarks: legacy.bookmark_count || 0,
      views: tweet.views?.count ? parseInt(tweet.views.count) : null
    };
  }

  /**
   * Extract basic URLs without expansion
   */
  extractBasicUrls(tweet) {
    const urls = [];
    const entities = tweet.legacy?.entities;
    
    if (entities?.urls) {
      entities.urls.forEach(url => {
        urls.push({
          url: url.url,
          expandedUrl: url.expanded_url,
          displayUrl: url.display_url,
          indices: url.indices
        });
      });
    }

    return urls;
  }

  /**
   * Determine tweet type
   */
  determineTweetType(tweet) {
    const legacy = tweet.legacy;
    if (!legacy) return 'unknown';

    if (legacy.retweeted_status_result) return 'retweet';
    if (tweet.quoted_status_result) return 'quote';
    if (legacy.in_reply_to_status_id_str) return 'reply';
    if (legacy.entities?.media?.length > 0) return 'media';
    
    return 'original';
  }
}

/**
 * Media extraction utilities
 */
class MediaExtractor {
  constructor() {
    this.logger = new Logger('MediaExtractor');
  }

  async extractMedia(tweet) {
    const media = [];
    const entities = tweet.legacy?.entities;
    const extendedEntities = tweet.legacy?.extended_entities;

    // Process media from entities
    if (entities?.media) {
      for (const mediaItem of entities.media) {
        media.push(await this.processMediaItem(mediaItem));
      }
    }

    // Process extended media (videos, GIFs)
    if (extendedEntities?.media) {
      for (const mediaItem of extendedEntities.media) {
        const processed = await this.processMediaItem(mediaItem, true);
        // Avoid duplicates
        if (!media.find(m => m.id === processed.id)) {
          media.push(processed);
        }
      }
    }

    return media;
  }

  async processMediaItem(mediaItem, isExtended = false) {
    const baseMedia = {
      id: mediaItem.id_str,
      type: mediaItem.type,
      url: mediaItem.media_url_https,
      displayUrl: mediaItem.display_url,
      expandedUrl: mediaItem.expanded_url,
      indices: mediaItem.indices,
      sizes: mediaItem.sizes
    };

    // Handle video/GIF specific data
    if (mediaItem.type === 'video' || mediaItem.type === 'animated_gif') {
      if (mediaItem.video_info) {
        baseMedia.videoInfo = {
          aspectRatio: mediaItem.video_info.aspect_ratio,
          durationMillis: mediaItem.video_info.duration_millis,
          variants: mediaItem.video_info.variants?.map(variant => ({
            bitrate: variant.bitrate,
            contentType: variant.content_type,
            url: variant.url
          }))
        };
      }
    }

    // Add alt text if available
    if (mediaItem.ext_alt_text) {
      baseMedia.altText = mediaItem.ext_alt_text;
    }

    return baseMedia;
  }
}

/**
 * URL processing utilities
 */
class URLProcessor {
  constructor() {
    this.logger = new Logger('URLProcessor');
  }

  async processUrls(tweet) {
    const urls = [];
    const entities = tweet.legacy?.entities;
    
    if (entities?.urls) {
      for (const url of entities.urls) {
        try {
          const processed = await this.expandUrl(url);
          urls.push(processed);
        } catch (error) {
          this.logger.warn('Failed to process URL:', url.url, error);
          urls.push({
            url: url.url,
            expandedUrl: url.expanded_url,
            displayUrl: url.display_url,
            indices: url.indices,
            error: error.message
          });
        }
      }
    }

    return urls;
  }

  async expandUrl(urlData) {
    // For now, return the expanded URL from Twitter's data
    // In a full implementation, this could make HTTP requests to get final URLs
    return {
      url: urlData.url,
      expandedUrl: urlData.expanded_url,
      displayUrl: urlData.display_url,
      indices: urlData.indices,
      finalUrl: urlData.expanded_url, // Would be resolved via HTTP request
      title: null, // Would be extracted from page
      description: null // Would be extracted from page
    };
  }
}

/**
 * Data validation utilities
 */
class DataValidator {
  constructor() {
    this.logger = new Logger('DataValidator');
  }

  validateBookmark(bookmark) {
    const required = ['id', 'platform', 'url', 'author', 'content'];
    
    for (const field of required) {
      if (!bookmark[field]) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    // Validate author
    if (!bookmark.author.username) {
      throw new Error('Author username is required');
    }

    // Validate content
    if (typeof bookmark.content.text !== 'string') {
      throw new Error('Content text must be a string');
    }

    return true;
  }
}
