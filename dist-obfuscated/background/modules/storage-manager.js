/**
 * Storage Manager - Handles all storage operations for AbstractBookmark
 */

import { Logger } from './logger.js';

export class StorageManager {
  constructor() {
    this.logger = new Logger('StorageManager');
    this.defaultSettings = {
      // Export settings
      exportFormat: 'json',
      includeMedia: true,
      includeMetadata: true,
      maxExportSize: 10000,

      // API settings
      requestDelay: 1000,
      maxRetries: 3,
      timeoutDuration: 30000,

      // UI settings
      theme: 'auto',
      language: 'en',
      languageAutoDetected: true, // Whether language was auto-detected from browser
      showNotifications: true,

      // Privacy settings
      storeCredentials: false,
      encryptData: true,

      // Performance settings
      batchSize: 20,
      maxConcurrentRequests: 3,

      // Platform-specific settings
      platforms: {
        twitter: {
          enabled: true,
          includeReplies: false,
          includeRetweets: true,
        },
        linkedin: {
          enabled: false,
        },
        instagram: {
          enabled: false,
        },
        reddit: {
          enabled: false,
        },
      },
    };
  }

  async initializeDefaultSettings() {
    try {
      this.logger.info('Initializing default settings');

      const { settings } = await chrome.storage.sync.get('settings');

      if (!settings) {
        await chrome.storage.sync.set({
          settings: this.defaultSettings,
          version: '1.0.0',
          installedAt: new Date().toISOString(),
        });

        this.logger.info('Default settings initialized');
      } else {
        this.logger.info('Settings already exist, skipping initialization');
      }
    } catch (error) {
      this.logger.error('Failed to initialize default settings:', error);
      throw error;
    }
  }

  async getSettings() {
    try {
      const { settings } = await chrome.storage.sync.get('settings');

      if (!settings) {
        await this.initializeDefaultSettings();
        return this.defaultSettings;
      }

      // Merge with defaults to ensure all properties exist
      return this.mergeWithDefaults(settings);
    } catch (error) {
      this.logger.error('Failed to get settings:', error);
      return this.defaultSettings;
    }
  }

  async updateSettings(newSettings) {
    try {
      const currentSettings = await this.getSettings();
      const mergedSettings = { ...currentSettings, ...newSettings };

      await chrome.storage.sync.set({
        settings: mergedSettings,
        updatedAt: new Date().toISOString(),
      });

      this.logger.info('Settings updated successfully');
      return mergedSettings;
    } catch (error) {
      this.logger.error('Failed to update settings:', error);
      throw error;
    }
  }

  async resetSettings() {
    try {
      await chrome.storage.sync.set({
        settings: this.defaultSettings,
        resetAt: new Date().toISOString(),
      });

      this.logger.info('Settings reset to defaults');
      return this.defaultSettings;
    } catch (error) {
      this.logger.error('Failed to reset settings:', error);
      throw error;
    }
  }

  async migrateSettings(previousVersion) {
    try {
      this.logger.info(`Migrating settings from version ${previousVersion}`);

      const { settings } = await chrome.storage.sync.get('settings');

      if (!settings) {
        await this.initializeDefaultSettings();
        return;
      }

      // Version-specific migrations
      let migratedSettings = { ...settings };

      // Example migration for version 0.9.x to 1.0.0
      if (this.isVersionLessThan(previousVersion, '1.0.0')) {
        // Add new settings that didn't exist in previous versions
        migratedSettings = this.mergeWithDefaults(migratedSettings);

        // Migrate old setting names
        if (settings.oldSettingName) {
          migratedSettings.newSettingName = settings.oldSettingName;
          delete migratedSettings.oldSettingName;
        }
      }

      await chrome.storage.sync.set({
        settings: migratedSettings,
        migratedFrom: previousVersion,
        migratedAt: new Date().toISOString(),
      });

      this.logger.info('Settings migration completed');
    } catch (error) {
      this.logger.error('Failed to migrate settings:', error);
      // Don't throw - fallback to default settings
      await this.initializeDefaultSettings();
    }
  }

  mergeWithDefaults(settings) {
    const merged = { ...this.defaultSettings };

    // Deep merge for nested objects
    for (const key in settings) {
      if (
        typeof settings[key] === 'object' &&
        settings[key] !== null &&
        !Array.isArray(settings[key])
      ) {
        merged[key] = { ...merged[key], ...settings[key] };
      } else {
        merged[key] = settings[key];
      }
    }

    return merged;
  }

  isVersionLessThan(version1, version2) {
    const v1Parts = version1.split('.').map(Number);
    const v2Parts = version2.split('.').map(Number);

    for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
      const v1Part = v1Parts[i] || 0;
      const v2Part = v2Parts[i] || 0;

      if (v1Part < v2Part) return true;
      if (v1Part > v2Part) return false;
    }

    return false;
  }

  // Bookmark storage methods
  async saveBookmark(bookmark) {
    try {
      const bookmarkId = this.generateBookmarkId(bookmark);
      const storageKey = `bookmark_${bookmarkId}`;

      const bookmarkData = {
        ...bookmark,
        id: bookmarkId,
        savedAt: new Date().toISOString(),
      };

      await chrome.storage.local.set({ [storageKey]: bookmarkData });

      // Update bookmark index
      await this.updateBookmarkIndex(bookmarkId, bookmark);

      this.logger.debug(`Bookmark saved: ${bookmarkId}`);
      return bookmarkId;
    } catch (error) {
      this.logger.error('Failed to save bookmark:', error);
      throw error;
    }
  }

  async getBookmark(bookmarkId) {
    try {
      const storageKey = `bookmark_${bookmarkId}`;
      const { [storageKey]: bookmark } =
        await chrome.storage.local.get(storageKey);
      return bookmark || null;
    } catch (error) {
      this.logger.error('Failed to get bookmark:', error);
      return null;
    }
  }

  async deleteBookmark(bookmarkId) {
    try {
      const storageKey = `bookmark_${bookmarkId}`;
      await chrome.storage.local.remove(storageKey);

      // Update bookmark index
      await this.removeFromBookmarkIndex(bookmarkId);

      this.logger.debug(`Bookmark deleted: ${bookmarkId}`);
    } catch (error) {
      this.logger.error('Failed to delete bookmark:', error);
      throw error;
    }
  }

  async updateBookmarkIndex(bookmarkId, bookmark) {
    try {
      const { bookmarkIndex = {} } =
        await chrome.storage.local.get('bookmarkIndex');

      bookmarkIndex[bookmarkId] = {
        platform: bookmark.platform,
        createdAt: bookmark.content?.createdAt,
        savedAt: new Date().toISOString(),
        author: bookmark.author?.username,
      };

      await chrome.storage.local.set({ bookmarkIndex });
    } catch (error) {
      this.logger.error('Failed to update bookmark index:', error);
    }
  }

  async removeFromBookmarkIndex(bookmarkId) {
    try {
      const { bookmarkIndex = {} } =
        await chrome.storage.local.get('bookmarkIndex');
      delete bookmarkIndex[bookmarkId];
      await chrome.storage.local.set({ bookmarkIndex });
    } catch (error) {
      this.logger.error('Failed to remove from bookmark index:', error);
    }
  }

  async getBookmarkIndex() {
    try {
      const { bookmarkIndex = {} } =
        await chrome.storage.local.get('bookmarkIndex');
      return bookmarkIndex;
    } catch (error) {
      this.logger.error('Failed to get bookmark index:', error);
      return {};
    }
  }

  generateBookmarkId(bookmark) {
    // Generate a unique ID based on platform and original ID
    const platformId = bookmark.platform || 'unknown';
    const originalId = bookmark.id || bookmark.url || Date.now().toString();
    return `${platformId}_${originalId}`;
  }

  // Export storage methods
  async saveExportData(exportId, data) {
    try {
      const storageKey = `export_${exportId}`;
      await chrome.storage.local.set({ [storageKey]: data });
      this.logger.debug(`Export data saved: ${exportId}`);
    } catch (error) {
      this.logger.error('Failed to save export data:', error);
      throw error;
    }
  }

  async getExportData(exportId) {
    try {
      const storageKey = `export_${exportId}`;
      const { [storageKey]: data } = await chrome.storage.local.get(storageKey);
      return data || null;
    } catch (error) {
      this.logger.error('Failed to get export data:', error);
      return null;
    }
  }

  async deleteExportData(exportId) {
    try {
      const storageKey = `export_${exportId}`;
      await chrome.storage.local.remove(storageKey);
      this.logger.debug(`Export data deleted: ${exportId}`);
    } catch (error) {
      this.logger.error('Failed to delete export data:', error);
    }
  }

  // Utility methods
  async getStorageUsage() {
    try {
      const usage = await chrome.storage.local.getBytesInUse();
      return {
        used: usage,
        quota: chrome.storage.local.QUOTA_BYTES,
        percentage: (usage / chrome.storage.local.QUOTA_BYTES) * 100,
      };
    } catch (error) {
      this.logger.error('Failed to get storage usage:', error);
      return { used: 0, quota: 0, percentage: 0 };
    }
  }

  async clearAllData() {
    try {
      await chrome.storage.local.clear();
      await chrome.storage.sync.clear();
      this.logger.info('All storage data cleared');
    } catch (error) {
      this.logger.error('Failed to clear storage data:', error);
      throw error;
    }
  }

  // Language Management Methods

  /**
   * Get current language setting
   * @returns {Promise<string>} Current language code
   */
  async getCurrentLanguage() {
    try {
      const settings = await this.getSettings();
      return settings.language || 'en';
    } catch (error) {
      this.logger.error('Failed to get current language:', error);
      return 'en';
    }
  }

  /**
   * Set language preference
   * @param {string} languageCode - Language code (e.g., 'en', 'tr')
   * @param {boolean} isAutoDetected - Whether this is an auto-detected language
   * @returns {Promise<void>}
   */
  async setLanguage(languageCode, isAutoDetected = false) {
    try {
      await this.updateSettings({
        language: languageCode,
        languageAutoDetected: isAutoDetected,
      });
      this.logger.info(
        `Language set to: ${languageCode} (auto: ${isAutoDetected})`
      );
    } catch (error) {
      this.logger.error('Failed to set language:', error);
      throw error;
    }
  }

  /**
   * Get available languages
   * @returns {Array} Array of available language objects
   */
  getAvailableLanguages() {
    return [
      { code: 'en', name: 'English' },
      { code: 'tr', name: 'Türkçe' },
      { code: 'es', name: 'Español' },
      { code: 'fr', name: 'Français' },
      { code: 'de', name: 'Deutsch' },
      { code: 'zh-CN', name: '中文 (简体)' },
      { code: 'ja', name: '日本語' },
      { code: 'ru', name: 'Русский' },
      { code: 'hi', name: 'हिन्दी' },
      { code: 'pt', name: 'Português' },
      { code: 'bn', name: 'বাংলা' },
      { code: 'vi', name: 'Tiếng Việt' },
      { code: 'th', name: 'ไทย' },
    ];
  }

  /**
   * Detect browser language and set if supported
   * @returns {Promise<string>} Detected/set language code
   */
  async detectBrowserLanguage() {
    try {
      const availableLanguages = this.getAvailableLanguages().map(
        (lang) => lang.code
      );

      // Get browser languages
      const acceptLanguages = await new Promise((resolve) => {
        chrome.i18n.getAcceptLanguages((languages) => {
          resolve(languages);
        });
      });

      // Get current UI language
      const currentUILanguage = chrome.i18n.getUILanguage();

      // Check current UI language first
      if (availableLanguages.includes(currentUILanguage)) {
        await this.setLanguage(currentUILanguage, true);
        return currentUILanguage;
      }

      // Check accept languages in order
      for (const lang of acceptLanguages) {
        // Check exact match
        if (availableLanguages.includes(lang)) {
          await this.setLanguage(lang, true);
          return lang;
        }

        // Check language without region (e.g., 'en' from 'en-US')
        const baseLang = lang.split('-')[0];
        if (availableLanguages.includes(baseLang)) {
          await this.setLanguage(baseLang, true);
          return baseLang;
        }
      }

      // Fallback to English
      await this.setLanguage('en', true);
      return 'en';
    } catch (error) {
      this.logger.error('Failed to detect browser language:', error);
      return 'en';
    }
  }

  /**
   * Initialize language setting on first run or when auto-detection is enabled
   * @returns {Promise<void>}
   */
  async initializeLanguage() {
    try {
      const settings = await this.getSettings();

      // If auto-detection is enabled (default) or language was never manually set
      if (settings.languageAutoDetected !== false) {
        const detectedLanguage = await this.detectBrowserLanguage();
        this.logger.info(`Language auto-detected to: ${detectedLanguage}`);
      } else {
        this.logger.info(
          `Language auto-detection disabled, using: ${settings.language}`
        );
      }
    } catch (error) {
      this.logger.error('Failed to initialize language:', error);
    }
  }

  /**
   * Check if browser language has changed and update if auto-detection is enabled
   * @returns {Promise<boolean>} True if language was updated
   */
  async checkAndUpdateBrowserLanguage() {
    try {
      const settings = await this.getSettings();

      // Only update if auto-detection is enabled
      if (settings.languageAutoDetected === false) {
        return false;
      }

      const currentLanguage = settings.language;
      const availableLanguages = this.getAvailableLanguages().map(
        (lang) => lang.code
      );

      // Get browser languages
      const acceptLanguages = await new Promise((resolve) => {
        chrome.i18n.getAcceptLanguages((languages) => {
          resolve(languages);
        });
      });

      // Get current UI language
      const currentUILanguage = chrome.i18n.getUILanguage();

      // Check if current UI language is different and supported
      if (
        availableLanguages.includes(currentUILanguage) &&
        currentUILanguage !== currentLanguage
      ) {
        await this.setLanguage(currentUILanguage, true);
        this.logger.info(`Language auto-updated to: ${currentUILanguage}`);
        return true;
      }

      // Check accept languages in order
      for (const lang of acceptLanguages) {
        if (lang === currentLanguage) {
          break; // Current language is still preferred
        }

        // Check exact match
        if (availableLanguages.includes(lang)) {
          await this.setLanguage(lang, true);
          this.logger.info(`Language auto-updated to: ${lang}`);
          return true;
        }

        // Check language without region
        const baseLang = lang.split('-')[0];
        if (
          availableLanguages.includes(baseLang) &&
          baseLang !== currentLanguage
        ) {
          await this.setLanguage(baseLang, true);
          this.logger.info(`Language auto-updated to: ${baseLang}`);
          return true;
        }
      }

      return false;
    } catch (error) {
      this.logger.error('Failed to check browser language:', error);
      return false;
    }
  }
}
