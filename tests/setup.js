/**
 * Jest Test Setup for AbstractBookmark
 */

// Mock Chrome APIs
global.chrome = {
  runtime: {
    onMessage: {
      addListener: jest.fn(),
      removeListener: jest.fn(),
    },
    onInstalled: {
      addListener: jest.fn(),
    },
    sendMessage: jest.fn(),
    getURL: jest.fn((path) => `chrome-extension://test-id/${path}`),
    id: 'test-extension-id',
  },
  storage: {
    local: {
      get: jest.fn(),
      set: jest.fn(),
      remove: jest.fn(),
      clear: jest.fn(),
      getBytesInUse: jest.fn(),
      QUOTA_BYTES: 5242880, // 5MB
    },
    sync: {
      get: jest.fn(),
      set: jest.fn(),
      remove: jest.fn(),
      clear: jest.fn(),
      getBytesInUse: jest.fn(),
      QUOTA_BYTES: 102400, // 100KB
    },
  },
  tabs: {
    onUpdated: {
      addListener: jest.fn(),
    },
    sendMessage: jest.fn(),
    create: jest.fn(),
    query: jest.fn(),
  },
  action: {
    setBadgeText: jest.fn(),
    setBadgeBackgroundColor: jest.fn(),
  },
  alarms: {
    onAlarm: {
      addListener: jest.fn(),
    },
    create: jest.fn(),
    clear: jest.fn(),
  },
  notifications: {
    create: jest.fn(),
  },
  scripting: {
    executeScript: jest.fn(),
  },
  webRequest: {
    onBeforeRequest: {
      addListener: jest.fn(),
    },
    onHeadersReceived: {
      addListener: jest.fn(),
    },
  },
};

// Mock fetch API
global.fetch = jest.fn();

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  time: jest.fn(),
  timeEnd: jest.fn(),
  group: jest.fn(),
  groupEnd: jest.fn(),
  table: jest.fn(),
};

// Mock Date.now for consistent testing
const mockDate = new Date('2024-01-01T00:00:00.000Z');
global.Date.now = jest.fn(() => mockDate.getTime());

// Mock crypto for ID generation
global.crypto = {
  randomUUID: jest.fn(() => 'test-uuid-1234'),
  getRandomValues: jest.fn((array) => {
    for (let i = 0; i < array.length; i++) {
      array[i] = Math.floor(Math.random() * 256);
    }
    return array;
  }),
};

// Test utilities
global.testUtils = {
  // Create mock bookmark data
  createMockBookmark: (overrides = {}) => ({
    id: 'test-bookmark-1',
    platform: 'twitter',
    author: {
      username: 'testuser',
      displayName: 'Test User',
      profileImage: 'https://example.com/avatar.jpg',
    },
    content: {
      text: 'This is a test bookmark',
      createdAt: '2024-01-01T00:00:00.000Z',
      lang: 'en',
    },
    media: [],
    metrics: {
      retweets: 10,
      likes: 50,
      replies: 5,
      quotes: 2,
    },
    urls: [],
    bookmarkedAt: '2024-01-01T00:00:00.000Z',
    ...overrides,
  }),

  // Create mock API response
  createMockAPIResponse: (bookmarks = [], hasMore = false) => ({
    data: {
      bookmark_timeline_v2: {
        timeline: {
          instructions: [
            {
              entries: [
                ...bookmarks.map((bookmark, index) => ({
                  entryId: `tweet-${bookmark.id}`,
                  sortIndex: `${Date.now() + index}`,
                  content: {
                    itemContent: {
                      tweet_results: {
                        result: {
                          rest_id: bookmark.id,
                          legacy: {
                            full_text: bookmark.content.text,
                            created_at: bookmark.content.createdAt,
                            retweet_count: bookmark.metrics.retweets,
                            favorite_count: bookmark.metrics.likes,
                            reply_count: bookmark.metrics.replies,
                            quote_count: bookmark.metrics.quotes,
                          },
                          core: {
                            user_results: {
                              result: {
                                legacy: {
                                  screen_name: bookmark.author.username,
                                  name: bookmark.author.displayName,
                                  profile_image_url_https: bookmark.author.profileImage,
                                },
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                })),
                ...(hasMore
                  ? [
                      {
                        entryId: 'cursor-bottom-123',
                        content: {
                          value: 'next-cursor-token',
                        },
                      },
                    ]
                  : []),
              ],
            },
          ],
        },
      },
    },
  }),

  // Create mock settings
  createMockSettings: (overrides = {}) => ({
    exportFormat: 'json',
    includeMedia: true,
    includeMetadata: true,
    maxExportSize: 10000,
    requestDelay: 1000,
    maxRetries: 3,
    timeoutDuration: 30000,
    theme: 'auto',
    language: 'en',
    showNotifications: true,
    storeCredentials: false,
    encryptData: true,
    batchSize: 20,
    maxConcurrentRequests: 3,
    platforms: {
      twitter: {
        enabled: true,
        includeReplies: false,
        includeRetweets: true,
      },
      linkedin: {
        enabled: false,
      },
      instagram: {
        enabled: false,
      },
      reddit: {
        enabled: false,
      },
    },
    ...overrides,
  }),

  // Wait for async operations
  waitFor: (ms = 0) => new Promise((resolve) => setTimeout(resolve, ms)),

  // Reset all mocks
  resetMocks: () => {
    jest.clearAllMocks();
    
    // Reset chrome API mocks to default behavior
    chrome.storage.local.get.mockResolvedValue({});
    chrome.storage.local.set.mockResolvedValue();
    chrome.storage.sync.get.mockResolvedValue({});
    chrome.storage.sync.set.mockResolvedValue();
    
    fetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({}),
    });
  },
};

// Setup before each test
beforeEach(() => {
  global.testUtils.resetMocks();
});

// Cleanup after each test
afterEach(() => {
  jest.clearAllTimers();
});
