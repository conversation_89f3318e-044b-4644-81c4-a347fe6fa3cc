/**
 * Service Worker Tests
 */

import { jest } from '@jest/globals';

// Mock the modules before importing the service worker
jest.mock('../../background/modules/bookmark-manager.js', () => ({
  BookmarkManager: jest.fn().mockImplementation(() => ({
    saveBookmark: jest.fn(),
    getStatistics: jest.fn()
  }))
}));

jest.mock('../../background/modules/api-manager.js', () => ({
  APIManager: jest.fn().mockImplementation(() => ({
    isPlatformSupported: jest.fn(),
    fetchBookmarks: jest.fn(),
    getSupportedPlatforms: jest.fn()
  }))
}));

jest.mock('../../background/modules/export-manager.js', () => ({
  ExportManager: jest.fn().mockImplementation(() => ({
    startExport: jest.fn(),
    updateExportStatus: jest.fn(),
    processBookmarks: jest.fn(),
    getExportProgress: jest.fn(),
    cancelExport: jest.fn()
  }))
}));

jest.mock('../../background/modules/storage-manager.js', () => ({
  StorageManager: jest.fn().mockImplementation(() => ({
    initializeDefaultSettings: jest.fn(),
    getSettings: jest.fn(),
    updateSettings: jest.fn(),
    migrateSettings: jest.fn()
  }))
}));

describe('AbstractBookmark Service Worker', () => {
  let mockBookmarkManager;
  let mockAPIManager;
  let mockExportManager;
  let mockStorageManager;

  beforeEach(() => {
    // Reset mocks
    global.testUtils.resetMocks();
    
    // Get mock instances
    const { BookmarkManager } = require('../../background/modules/bookmark-manager.js');
    const { APIManager } = require('../../background/modules/api-manager.js');
    const { ExportManager } = require('../../background/modules/export-manager.js');
    const { StorageManager } = require('../../background/modules/storage-manager.js');
    
    mockBookmarkManager = new BookmarkManager();
    mockAPIManager = new APIManager();
    mockExportManager = new ExportManager();
    mockStorageManager = new StorageManager();
  });

  describe('Initialization', () => {
    test('should initialize service worker without errors', () => {
      expect(() => {
        require('../../background/service-worker.js');
      }).not.toThrow();
    });

    test('should register message listeners', () => {
      require('../../background/service-worker.js');
      
      expect(chrome.runtime.onMessage.addListener).toHaveBeenCalled();
      expect(chrome.runtime.onInstalled.addListener).toHaveBeenCalled();
      expect(chrome.tabs.onUpdated.addListener).toHaveBeenCalled();
      expect(chrome.alarms.onAlarm.addListener).toHaveBeenCalled();
    });
  });

  describe('Message Handling', () => {
    let messageHandler;

    beforeEach(() => {
      require('../../background/service-worker.js');
      
      // Get the message handler from the mock
      const calls = chrome.runtime.onMessage.addListener.mock.calls;
      messageHandler = calls[0][0];
    });

    test('should handle health_check message', async () => {
      const mockSendResponse = jest.fn();
      const message = { action: 'health_check' };
      const sender = { tab: { id: 1 } };

      await messageHandler(message, sender, mockSendResponse);

      expect(mockSendResponse).toHaveBeenCalledWith({
        success: true,
        status: 'healthy',
        timestamp: expect.any(Number)
      });
    });

    test('should handle start_export message', async () => {
      const mockSendResponse = jest.fn();
      const message = {
        action: 'start_export',
        platform: 'twitter',
        options: { maxCount: 100 }
      };
      const sender = { tab: { id: 1 } };

      mockAPIManager.isPlatformSupported.mockReturnValue(true);
      mockExportManager.startExport.mockResolvedValue('export-123');

      await messageHandler(message, sender, mockSendResponse);

      expect(mockAPIManager.isPlatformSupported).toHaveBeenCalledWith('twitter');
      expect(mockExportManager.startExport).toHaveBeenCalledWith('twitter', { maxCount: 100 });
      expect(mockSendResponse).toHaveBeenCalledWith({
        success: true,
        exportId: 'export-123'
      });
    });

    test('should handle unsupported platform error', async () => {
      const mockSendResponse = jest.fn();
      const message = {
        action: 'start_export',
        platform: 'unsupported',
        options: {}
      };
      const sender = { tab: { id: 1 } };

      mockAPIManager.isPlatformSupported.mockReturnValue(false);

      await messageHandler(message, sender, mockSendResponse);

      expect(mockSendResponse).toHaveBeenCalledWith({
        success: false,
        error: 'Unsupported platform: unsupported'
      });
    });

    test('should handle get_settings message', async () => {
      const mockSendResponse = jest.fn();
      const message = { action: 'get_settings' };
      const sender = { tab: { id: 1 } };
      const mockSettings = global.testUtils.createMockSettings();

      mockStorageManager.getSettings.mockResolvedValue(mockSettings);

      await messageHandler(message, sender, mockSendResponse);

      expect(mockStorageManager.getSettings).toHaveBeenCalled();
      expect(mockSendResponse).toHaveBeenCalledWith({
        success: true,
        settings: mockSettings
      });
    });

    test('should handle update_settings message', async () => {
      const mockSendResponse = jest.fn();
      const newSettings = { theme: 'dark' };
      const message = {
        action: 'update_settings',
        settings: newSettings
      };
      const sender = { tab: { id: 1 } };

      mockStorageManager.updateSettings.mockResolvedValue();

      await messageHandler(message, sender, mockSendResponse);

      expect(mockStorageManager.updateSettings).toHaveBeenCalledWith(newSettings);
      expect(mockSendResponse).toHaveBeenCalledWith({ success: true });
    });

    test('should handle get_statistics message', async () => {
      const mockSendResponse = jest.fn();
      const message = { action: 'get_statistics' };
      const sender = { tab: { id: 1 } };
      const mockStats = {
        total: 100,
        byPlatform: { twitter: 100 },
        byMonth: { '2024-01': 100 }
      };

      mockBookmarkManager.getStatistics.mockResolvedValue(mockStats);

      await messageHandler(message, sender, mockSendResponse);

      expect(mockBookmarkManager.getStatistics).toHaveBeenCalled();
      expect(mockSendResponse).toHaveBeenCalledWith({
        success: true,
        statistics: mockStats
      });
    });

    test('should handle get_supported_platforms message', async () => {
      const mockSendResponse = jest.fn();
      const message = { action: 'get_supported_platforms' };
      const sender = { tab: { id: 1 } };
      const mockPlatforms = [
        { key: 'twitter', name: 'Twitter/X', domain: 'twitter.com' }
      ];

      mockAPIManager.getSupportedPlatforms.mockReturnValue(mockPlatforms);

      await messageHandler(message, sender, mockSendResponse);

      expect(mockAPIManager.getSupportedPlatforms).toHaveBeenCalled();
      expect(mockSendResponse).toHaveBeenCalledWith({
        success: true,
        platforms: mockPlatforms
      });
    });

    test('should handle unknown action', async () => {
      const mockSendResponse = jest.fn();
      const message = { action: 'unknown_action' };
      const sender = { tab: { id: 1 } };

      await messageHandler(message, sender, mockSendResponse);

      expect(mockSendResponse).toHaveBeenCalledWith({
        success: false,
        error: 'Unknown action'
      });
    });

    test('should handle errors gracefully', async () => {
      const mockSendResponse = jest.fn();
      const message = { action: 'get_settings' };
      const sender = { tab: { id: 1 } };

      mockStorageManager.getSettings.mockRejectedValue(new Error('Storage error'));

      await messageHandler(message, sender, mockSendResponse);

      expect(mockSendResponse).toHaveBeenCalledWith({
        success: false,
        error: 'Storage error'
      });
    });
  });

  describe('Installation Handling', () => {
    let installHandler;

    beforeEach(() => {
      require('../../background/service-worker.js');
      
      const calls = chrome.runtime.onInstalled.addListener.mock.calls;
      installHandler = calls[0][0];
    });

    test('should handle first installation', async () => {
      const details = { reason: 'install' };

      mockStorageManager.initializeDefaultSettings.mockResolvedValue();

      await installHandler(details);

      expect(mockStorageManager.initializeDefaultSettings).toHaveBeenCalled();
      expect(chrome.tabs.create).toHaveBeenCalledWith({
        url: 'chrome-extension://test-id/options/welcome.html'
      });
    });

    test('should handle extension update', async () => {
      const details = { reason: 'update', previousVersion: '0.9.0' };

      mockStorageManager.migrateSettings.mockResolvedValue();

      await installHandler(details);

      expect(mockStorageManager.migrateSettings).toHaveBeenCalledWith('0.9.0');
      expect(chrome.tabs.create).not.toHaveBeenCalled();
    });
  });

  describe('Tab Update Handling', () => {
    let tabUpdateHandler;

    beforeEach(() => {
      require('../../background/service-worker.js');
      
      const calls = chrome.tabs.onUpdated.addListener.mock.calls;
      tabUpdateHandler = calls[0][0];
      
      mockAPIManager.getSupportedPlatforms.mockReturnValue([
        { key: 'twitter', name: 'Twitter/X', domain: 'twitter.com' }
      ]);
    });

    test('should set badge for supported platform', async () => {
      const tabId = 1;
      const changeInfo = { status: 'complete' };
      const tab = { url: 'https://twitter.com/bookmarks' };

      await tabUpdateHandler(tabId, changeInfo, tab);

      expect(chrome.action.setBadgeText).toHaveBeenCalledWith({
        tabId: 1,
        text: '●'
      });
      expect(chrome.action.setBadgeBackgroundColor).toHaveBeenCalledWith({
        tabId: 1,
        color: '#4CAF50'
      });
    });

    test('should clear badge for unsupported platform', async () => {
      const tabId = 1;
      const changeInfo = { status: 'complete' };
      const tab = { url: 'https://example.com' };

      await tabUpdateHandler(tabId, changeInfo, tab);

      expect(chrome.action.setBadgeText).toHaveBeenCalledWith({
        tabId: 1,
        text: ''
      });
    });

    test('should ignore incomplete tab updates', async () => {
      const tabId = 1;
      const changeInfo = { status: 'loading' };
      const tab = { url: 'https://twitter.com' };

      await tabUpdateHandler(tabId, changeInfo, tab);

      expect(chrome.action.setBadgeText).not.toHaveBeenCalled();
    });
  });
});
