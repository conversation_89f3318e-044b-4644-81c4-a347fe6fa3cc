/**
 * Twitter API Tests
 */

// Mock the modules
jest.mock('../../background/modules/logger.js', () => ({
  Logger: jest.fn().mockImplementation(() => ({
    info: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  })),
}));

describe('Twitter API Integration', () => {
  let TwitterAPI;
  let twitterApi;
  let mockCredentials;

  beforeEach(async () => {
    // Reset mocks
    global.testUtils.resetMocks();

    // Setup mock credentials
    mockCredentials = {
      authorization:
        'Bearer AAAAAAAAAAAAAAAAAAAAAMLheAAAAAAA0%2BuSeid%2BULvsea4JtiGRiSDSJSI%3DEUifiRBkKG5E2XzMDjRfl76ZC9Ub0wnz4XsNiRVBChTYbJcE3F',
      csrfToken: 'a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0',
    };

    // Mock chrome.tabs.query
    chrome.tabs.query.mockResolvedValue([
      { id: 1, url: 'https://x.com/i/bookmarks' },
    ]);

    // Mock chrome.scripting.executeScript
    chrome.scripting.executeScript.mockResolvedValue([
      {
        result: mockCredentials,
      },
    ]);

    // Import and create API instance
    const { APIManager } = await import(
      '../../background/modules/api-manager.js'
    );
    const apiManager = new APIManager();
    TwitterAPI = apiManager.platforms.get('twitter').apiClass;
    twitterApi = new TwitterAPI();
  });

  describe('Credential Extraction', () => {
    test('should extract credentials from active Twitter tab', async () => {
      const credentials =
        await twitterApi.credentialExtractor.extractCredentials();

      expect(chrome.tabs.query).toHaveBeenCalledWith({
        active: true,
        currentWindow: true,
        url: ['*://twitter.com/*', '*://x.com/*'],
      });

      expect(chrome.scripting.executeScript).toHaveBeenCalled();
      expect(credentials).toEqual(mockCredentials);
    });

    test('should throw error when no Twitter tab is found', async () => {
      chrome.tabs.query.mockResolvedValue([]);

      await expect(
        twitterApi.credentialExtractor.extractCredentials()
      ).rejects.toThrow('No active Twitter tab found');
    });

    test('should validate credentials correctly', () => {
      expect(() =>
        twitterApi.credentialExtractor.validateCredentials(mockCredentials)
      ).not.toThrow();
    });

    test('should throw error for invalid credentials', () => {
      const invalidCredentials = {
        authorization: 'Invalid token',
        csrfToken: 'valid-csrf',
      };

      expect(() =>
        twitterApi.credentialExtractor.validateCredentials(invalidCredentials)
      ).toThrow('Valid authorization token is required');
    });
  });

  describe('Rate Limiting', () => {
    test('should track request times', async () => {
      await twitterApi.rateLimiter.checkRateLimit();

      expect(twitterApi.rateLimiter.requestTimes.length).toBe(1);
    });

    test('should prevent rate limit violations', async () => {
      // Fill up the rate limiter
      for (let i = 0; i < 70; i++) {
        twitterApi.rateLimiter.requestTimes.push(Date.now());
      }

      await expect(twitterApi.rateLimiter.checkRateLimit()).rejects.toThrow(
        'Rate limit prevention'
      );
    });

    test('should calculate dynamic delays', () => {
      // Empty rate limiter should return base delay
      const baseDelay = twitterApi.rateLimiter.getNextDelay();
      expect(baseDelay).toBeGreaterThanOrEqual(1000);

      // Full rate limiter should return higher delay
      for (let i = 0; i < 50; i++) {
        twitterApi.rateLimiter.requestTimes.push(Date.now());
      }

      const highDelay = twitterApi.rateLimiter.getNextDelay();
      expect(highDelay).toBeGreaterThan(baseDelay);
    });
  });

  describe('API Requests', () => {
    beforeEach(() => {
      // Mock successful API response
      const mockResponse = global.testUtils.createMockAPIResponse(
        [
          global.testUtils.createMockBookmark({ id: '1' }),
          global.testUtils.createMockBookmark({ id: '2' }),
        ],
        true
      );

      global.fetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse),
        headers: new Map(),
      });
    });

    test('should fetch bookmark page successfully', async () => {
      const response = await twitterApi.fetchBookmarkPage(
        null,
        mockCredentials
      );

      expect(global.fetch).toHaveBeenCalled();
      expect(response.data).toBeDefined();
      expect(response.data.bookmark_timeline_v2).toBeDefined();
    });

    test('should handle cursor-based pagination', async () => {
      const cursor = 'test-cursor-123';
      await twitterApi.fetchBookmarkPage(cursor, mockCredentials);

      const fetchCall = global.fetch.mock.calls[0];
      const url = fetchCall[0];

      expect(url).toContain(
        encodeURIComponent(
          JSON.stringify({
            count: 20,
            includePromotedContent: false,
            cursor: cursor,
          })
        )
      );
    });

    test('should include proper headers', async () => {
      await twitterApi.fetchBookmarkPage(null, mockCredentials);

      const fetchCall = global.fetch.mock.calls[0];
      const options = fetchCall[1];

      expect(options.headers.authorization).toBe(mockCredentials.authorization);
      expect(options.headers['x-csrf-token']).toBe(mockCredentials.csrfToken);
      expect(options.headers['x-twitter-active-user']).toBe('yes');
    });

    test('should handle API errors', async () => {
      global.fetch.mockResolvedValue({
        ok: false,
        status: 401,
        statusText: 'Unauthorized',
        text: () => Promise.resolve('Unauthorized'),
      });

      await expect(
        twitterApi.fetchBookmarkPage(null, mockCredentials)
      ).rejects.toThrow('Authentication failed');
    });

    test('should handle rate limit errors', async () => {
      global.fetch.mockResolvedValue({
        ok: false,
        status: 429,
        statusText: 'Too Many Requests',
        text: () => Promise.resolve('Rate limit exceeded'),
        headers: new Map([
          ['x-rate-limit-reset', '1640995200'],
          ['x-rate-limit-remaining', '0'],
        ]),
      });

      await expect(
        twitterApi.fetchBookmarkPage(null, mockCredentials)
      ).rejects.toThrow('Rate limit exceeded');
    });
  });

  describe('Bookmark Processing', () => {
    test('should process Twitter bookmarks correctly', () => {
      const mockEntries = [
        {
          entryId: 'tweet-123',
          content: {
            itemContent: {
              tweet_results: {
                result: {
                  rest_id: '123',
                  legacy: {
                    full_text: 'Test tweet content',
                    created_at: 'Wed Oct 05 20:00:00 +0000 2023',
                    retweet_count: 10,
                    favorite_count: 50,
                    reply_count: 5,
                    quote_count: 2,
                  },
                  core: {
                    user_results: {
                      result: {
                        legacy: {
                          screen_name: 'testuser',
                          name: 'Test User',
                          profile_image_url_https:
                            'https://example.com/avatar.jpg',
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      ];

      const processed = twitterApi.processTwitterBookmarks(mockEntries);

      expect(processed).toHaveLength(1);
      expect(processed[0]).toMatchObject({
        id: '123',
        platform: 'twitter',
        author: {
          username: 'testuser',
          displayName: 'Test User',
        },
        content: {
          text: 'Test tweet content',
        },
        metrics: {
          retweets: 10,
          likes: 50,
          replies: 5,
          quotes: 2,
        },
      });
    });

    test('should extract media from tweets', () => {
      const entities = {
        media: [
          {
            type: 'photo',
            media_url_https: 'https://example.com/image.jpg',
            display_url: 'pic.twitter.com/abc123',
          },
        ],
      };

      const media = twitterApi.extractMedia(entities);

      expect(media).toHaveLength(1);
      expect(media[0]).toMatchObject({
        type: 'photo',
        url: 'https://example.com/image.jpg',
        displayUrl: 'pic.twitter.com/abc123',
      });
    });

    test('should extract URLs from tweets', () => {
      const entities = {
        urls: [
          {
            url: 'https://t.co/abc123',
            expanded_url: 'https://example.com/full-url',
            display_url: 'example.com/full-url',
          },
        ],
      };

      const urls = twitterApi.extractUrls(entities);

      expect(urls).toHaveLength(1);
      expect(urls[0]).toMatchObject({
        shortUrl: 'https://t.co/abc123',
        expandedUrl: 'https://example.com/full-url',
        displayUrl: 'example.com/full-url',
      });
    });
  });

  describe('Full Bookmark Fetch', () => {
    test('should fetch all bookmarks with progress callback', async () => {
      const progressCallback = jest.fn();
      const options = {
        credentials: mockCredentials,
        maxCount: 5,
      };

      // Mock multiple pages of responses
      const responses = [
        global.testUtils.createMockAPIResponse(
          [
            global.testUtils.createMockBookmark({ id: '1' }),
            global.testUtils.createMockBookmark({ id: '2' }),
          ],
          true
        ),
        global.testUtils.createMockAPIResponse(
          [
            global.testUtils.createMockBookmark({ id: '3' }),
            global.testUtils.createMockBookmark({ id: '4' }),
          ],
          true
        ),
        global.testUtils.createMockAPIResponse(
          [global.testUtils.createMockBookmark({ id: '5' })],
          false
        ),
      ];

      let callCount = 0;
      global.fetch.mockImplementation(() => {
        const response = responses[callCount++];
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(response),
          headers: new Map(),
        });
      });

      const bookmarks = await twitterApi.fetchAllBookmarks(
        options,
        progressCallback
      );

      expect(bookmarks).toHaveLength(5);
      expect(progressCallback).toHaveBeenCalled();
      expect(global.fetch).toHaveBeenCalledTimes(3);
    });

    test('should handle retry logic for retryable errors', async () => {
      const options = {
        credentials: mockCredentials,
        maxRetries: 2,
      };

      // First call fails, second succeeds
      global.fetch
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({
          ok: true,
          json: () =>
            Promise.resolve(global.testUtils.createMockAPIResponse([], false)),
          headers: new Map(),
        });

      const bookmarks = await twitterApi.fetchAllBookmarks(options);

      expect(bookmarks).toHaveLength(0);
      expect(global.fetch).toHaveBeenCalledTimes(2);
    });

    test('should stop at max retry limit', async () => {
      const options = {
        credentials: mockCredentials,
        maxRetries: 2,
      };

      // All calls fail
      global.fetch.mockRejectedValue(new Error('Network error'));

      await expect(twitterApi.fetchAllBookmarks(options)).rejects.toThrow(
        'Failed to fetch bookmarks after 2 retries'
      );
    });
  });
});
